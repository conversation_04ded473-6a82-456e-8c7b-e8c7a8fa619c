import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // إضافة استيراد للتحكم في اتجاه الشاشة
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../../providers/mosque_provider.dart';
import 'video_fullscreen_page.dart'; // استيراد صفحة عرض الفيديو بملء الشاشة

class LiveStreamTab extends StatefulWidget {
  const LiveStreamTab({super.key});

  @override
  State<LiveStreamTab> createState() => _LiveStreamTabState();
}

class _LiveStreamTabState extends State<LiveStreamTab> {
  late YoutubePlayerController _youtubeController;

  String _videoId = 'AG3Ewuhslp4'; // القيمة الافتراضية
  String _streamStatus = 'البث متاح';
  String _streamQuality = 'HD عالية الدقة';
  String _audioInfo = 'متوفر خلال الصلوات والأذان';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    // جلب بيانات البث المباشر من المزود
    _loadLiveStreamData();

    // تهيئة مشغل اليوتيوب مع تعطيل التشغيل التلقائي
    _youtubeController = YoutubePlayerController(
      initialVideoId: _videoId, // سيتم تحديثه لاحقًا
      flags: const YoutubePlayerFlags(
        autoPlay: false, // تغيير إلى false لمنع التشغيل التلقائي
        mute: false,
        disableDragSeek: true, // تعطيل إمكانية السحب في شريط التقدم
        loop: false,
        isLive: true,
        forceHD: false,
        enableCaption: false,
        hideControls: true, // إخفاء عناصر التحكم الافتراضية
      ),
    );

    // إضافة مستمع لحدث الخروج من وضع ملء الشاشة
    _youtubeController.addListener(() {
      if (_youtubeController.value.isFullScreen == false) {
        // إعادة تعيين اتجاه الشاشة إلى الوضع العمودي فقط عند الخروج من وضع ملء الشاشة
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
      }
    });
  }

  // جلب بيانات البث المباشر من المزود
  Future<void> _loadLiveStreamData() async {
    try {
      final mosqueProvider = Provider.of<MosqueProvider>(context, listen: false);

      // انتظار حتى تكون البيانات جاهزة
      if (mosqueProvider.liveStreamData == null) {
        await Future.delayed(const Duration(milliseconds: 500));
        if (!mounted) return;
        return _loadLiveStreamData();
      }

      // تحديث البيانات
      final liveStreamData = mosqueProvider.liveStreamData!;
      setState(() {
        _videoId = liveStreamData.videoId;
        _streamStatus = liveStreamData.status;
        _streamQuality = liveStreamData.quality;
        _audioInfo = liveStreamData.audioInfo;
        _isLoading = false;

        // تحديث مشغل اليوتيوب
        _youtubeController.load(_videoId);
      });
    } catch (e) {
      // استخدام البيانات الافتراضية في حالة حدوث خطأ
      setState(() {
        _isLoading = false;
      });
      print('خطأ في تحميل بيانات البث المباشر: $e');
    }
  }

  @override
  void dispose() {
    _youtubeController.dispose();
    // إعادة تعيين اتجاه الشاشة إلى الوضع العمودي فقط عند الخروج من الصفحة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام SingleChildScrollView لمنع مشكلة التجاوز
    return SingleChildScrollView(
      child: Column(
        children: [
          // إضافة مساحة في الأعلى
          const SizedBox(height: 16),

          // مشغل الفيديو
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // مؤشر التحميل
                    if (_isLoading)
                      const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF4F908E),
                        ),
                      ),

                    // مشغل الفيديو
                    GestureDetector(
                      onTap: () {
                        // تبديل حالة التشغيل عند النقر على الفيديو
                        if (_youtubeController.value.isPlaying) {
                          _youtubeController.pause();
                        } else {
                          _youtubeController.play();
                        }
                        setState(() {});
                      },
                      child: YoutubePlayer(
                        controller: _youtubeController,
                        showVideoProgressIndicator: false, // إخفاء شريط التقدم للبث المباشر
                        progressColors: null, // تعطيل ألوان شريط التقدم
                        // تعطيل شريط التقدم تمامًا للبث المباشر
                        onReady: () {
                          // لا نحتاج لشريط التقدم في البث المباشر
                        },
                        // إزالة bottomActions لتجنب مشاكل شريط التقدم
                      ),
                    ),

                    // زر تشغيل/إيقاف مخصص
                    ValueListenableBuilder<YoutubePlayerValue>(
                      valueListenable: _youtubeController,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: !value.isPlaying && !_isLoading,
                          child: Center(
                            child: GestureDetector(
                              onTap: () {
                                _youtubeController.play();
                              },
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.6),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 10,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.play_arrow,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    // إضافة علامة البث المباشر كعنصر منفصل
                    Positioned(
                      bottom: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.circle, color: Colors.red, size: 12),
                            SizedBox(width: 5),
                            Text(
                              'بث مباشر',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // زر التكبير (مثل صفحة الإرشادات)
                    Positioned(
                      bottom: 10,
                      right: 10,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 8,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.fullscreen, color: Colors.white),
                          onPressed: () {
                            // فتح صفحة جديدة لعرض الفيديو بملء الشاشة
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => VideoFullscreenPage(
                                  videoId: _videoId, // استخدام معرف الفيديو من البيانات
                                  autoPlay: _youtubeController.value.isPlaying, // استمرار التشغيل إذا كان الفيديو يعمل
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // معلومات البث
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildLiveInfo(),
          ),

          // إضافة مساحة في الأسفل
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildLiveInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4F908E).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Color(0xFF4F908E),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'معلومات البث',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3142),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow(Icons.schedule, 'البث متاح', _streamStatus),
          _buildInfoRow(Icons.hd, 'جودة البث', _streamQuality),
          _buildInfoRow(
            Icons.volume_up,
            'الصوت',
            _audioInfo,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: const Color(0xFF4F908E),
          ),
          const SizedBox(width: 8),
          Text(
            '$title: ',
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.ibmPlexSansArabic(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3142),
            ),
          ),
        ],
      ),
    );
  }
}
