import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  String _currentLanguage = 'ar';  // اللغة الافتراضية
  final String _prefsKey = 'app_language';

  LanguageProvider() {
    _loadSavedLanguage();
  }

  String get currentLanguage => _currentLanguage;

  Future<void> _loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    _currentLanguage = prefs.getString(_prefsKey) ?? 'ar';
    notifyListeners();
  }

  Future<void> changeLanguage(String languageCode) async {
    if (_currentLanguage != languageCode) {
      _currentLanguage = languageCode;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_prefsKey, languageCode);
      notifyListeners();
    }
  }

  TextDirection get textDirection {
    // اللغات التي تدعم RTL
    final rtlLanguages = ['ar', 'ur'];
    return rtlLanguages.contains(_currentLanguage) 
        ? TextDirection.rtl 
        : TextDirection.ltr;
  }
}
