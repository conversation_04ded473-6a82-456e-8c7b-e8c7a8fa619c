import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/explore/explore_data.dart';
import 'package:wiffada/screen/explore/explore_data_details_screen.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:provider/provider.dart';

class ExplorePage extends StatefulWidget {
  const ExplorePage({super.key});

  @override
  State<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends State<ExplorePage>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> sections = [];
  bool isLoading = true;
  late AppLocalizations translations;
  late AnimationController _backgroundAnimationController;
  late AnimationController _floatingAnimationController;

  @override
  void initState() {
    super.initState();

    _backgroundAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _floatingAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    placesDetales();
  }

  @override
  void dispose() {
    _backgroundAnimationController.dispose();
    _floatingAnimationController.dispose();
    super.dispose();
  }

  Future<void> placesDetales() async {
    try {
      final String response =
          await rootBundle.loadString('assets/destinations.json');
      final List<dynamic> categoriesList = json.decode(response);
      final loadedSections = <Map<String, dynamic>>[];

      final Map<String, String> categoryTitles = {
        'prophets-holy-osque': translations.translate('prophets_mosque'),
        'qibaa': translations.translate('quba_mosque'),
        'mosques': translations.translate('historical_mosques'),
        'wells-gardens': translations.translate('wells_gardens'),
        'historical-locations': translations.translate('historical_sites'),
        'museums-exhibitions': translations.translate('museums'),
        'parks-walkways': translations.translate('parks'),
        'malls-markets': translations.translate('markets'),
      };

      for (var categoryData in categoriesList) {
        if (categoryData != null &&
            categoryData['data'] != null &&
            categoryData['data'] is List &&
            categoryData['data'].isNotEmpty) {
          String categoryTitle = translations.translate('unknown_category');
          final firstItem = categoryData['data'][0];

          if (firstItem != null &&
              firstItem['category_slug'] != null &&
              categoryTitles.containsKey(firstItem['category_slug'])) {
            categoryTitle = categoryTitles[firstItem['category_slug']]!;
          }

          if (categoryData['data'].isNotEmpty) {
            loadedSections
                .add(_buildSectionData(categoryTitle, categoryData['data']));
          }
        }
      }

      if (mounted) {
        setState(() {
          sections = loadedSections;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          sections = _getDefaultSections();
          isLoading = false;
        });
      }
    }
  }

  Map<String, dynamic> _buildSectionData(String title, List<dynamic> items) {
    return {
      'title': title,
      'destinations': items.map<Map<String, dynamic>>((item) {
        String itemTitle = translations.translate('unknown');
        if (item['title'] != null) {
          if (item['title'] is String) {
            itemTitle = item['title'];
          } else if (item['title'] is Map) {
            if (item['title']['ar'] != null) {
              itemTitle = item['title']['ar'];
            } else if (item['title']['urd'] != null) {
              itemTitle =
                  item['title']['urd'] ?? translations.translate('unknown');
            }
          }
        } else if (item['name'] != null) {
          itemTitle = item['name'];
        }

        String imageUrl = 'https://via.placeholder.com/300x150?text=No+Image';
        if (item['image'] != null) {
          imageUrl = item['image'];
        } else if (item['images'] != null &&
            item['images'] is List &&
            item['images'].isNotEmpty) {
          imageUrl = item['images'][0];
        }

        return {
          'title': itemTitle,
          'location': translations.translate('madinah'),
          'image': imageUrl,
          'route': '/destination/${item['_id'] ?? ''}',
          'fullData': item,
        };
      }).toList(),
    };
  }

  List<Map<String, dynamic>> _getDefaultSections() {
    return [
      {
        'title': translations.translate('prophets_mosque'),
        'destinations': [
          {
            'title': translations.translate('prophets_chamber'),
            'location': translations.translate('madinah'),
            'image': 'https://visitmadinahsa.com/Areas/2512202313362188.webp',
            'route': '/prophets-mosque/chamber',
          },
          {
            'title': translations.translate('rawdah'),
            'location': translations.translate('madinah'),
            'image':
                'https://cdn.al-ain.com/images/2018/4/22/119-192708-al-rawdah-al-sharifah-garden-prophet-mosque_700x400.jpg',
            'route': '/prophets-mosque/rawdah',
          },
        ],
      },
      {
        'title': translations.translate('quba_mosque'),
        'destinations': [
          {
            'title': translations.translate('quba_mosque'),
            'location': translations.translate('madinah'),
            'image':
                'https://salehmcc.com/wp-content/uploads/2023/11/مشروع-مسجد-قباء.jpg',
            'route': '/quba/mosque',
          },
        ],
      },
    ];
  }

  Widget _buildShimmerLoading() {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 70,
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(25)),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.explore,
                color: Colors.white,
                size: 22,
              ),
            ),
            const SizedBox(width: 10),
            Text(
              translations.translate('explore'),
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.only(right: 12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(
              Icons.search,
              color: Colors.white,
            ),
            onPressed: () {},
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.2),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/wifada_logo.png',
                  width: 32,
                  height: 32,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
        ),
        child: CustomScrollView(
          physics: const NeverScrollableScrollPhysics(),
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.fromLTRB(20, 24, 20, 0),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF4F908E),
                      const Color(0xFF4F908E).withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4F908E).withOpacity(0.3),
                      offset: const Offset(0, 4),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Shimmer.fromColors(
                            baseColor: Colors.white.withOpacity(0.5),
                            highlightColor: Colors.white.withOpacity(0.8),
                            child: Container(
                              height: 20,
                              width: 120,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Shimmer.fromColors(
                            baseColor: Colors.white.withOpacity(0.5),
                            highlightColor: Colors.white.withOpacity(0.8),
                            child: Container(
                              height: 28,
                              width: 200,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Shimmer.fromColors(
                            baseColor: Colors.white.withOpacity(0.5),
                            highlightColor: Colors.white.withOpacity(0.8),
                            child: Container(
                              height: 16,
                              width: 180,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Shimmer.fromColors(
                      baseColor: Colors.white.withOpacity(0.5),
                      highlightColor: Colors.white.withOpacity(0.8),
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, sectionIndex) {
                  return Container(
                    margin: const EdgeInsets.only(top: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 14),
                              Expanded(
                                child: Shimmer.fromColors(
                                  baseColor: Colors.grey[300]!,
                                  highlightColor: Colors.grey[100]!,
                                  child: Container(
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                              Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: 80,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 220,
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            scrollDirection: Axis.horizontal,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: 3,
                            itemBuilder: (context, index) => Container(
                              margin: const EdgeInsets.only(right: 16),
                              width: 220,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    offset: const Offset(0, 4),
                                    blurRadius: 15,
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(24),
                                child: Shimmer.fromColors(
                                  baseColor: Colors.grey[300]!,
                                  highlightColor: Colors.grey[100]!,
                                  child: Container(
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                childCount: 3,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    if (isLoading) {
      return _buildShimmerLoading();
    }

    return Scaffold(
        appBar: AppBar(
          toolbarHeight: 100,
          backgroundColor: Colors.transparent,
          elevation: 0,
          flexibleSpace: AnimatedBuilder(
            animation: _backgroundAnimationController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF1A5F5D),
                      const Color(0xFF2D7A77),
                      const Color(0xFF4F908E),
                      Color.lerp(
                        const Color(0xFF6BA8A6),
                        const Color(0xFF4F908E),
                        0.5 +
                            0.5 *
                                math.sin(_backgroundAnimationController.value *
                                    2 *
                                    math.pi),
                      )!,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2D7A77).withValues(alpha: 0.4),
                      blurRadius: 25,
                      spreadRadius: 0,
                      offset: const Offset(0, 12),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 4),
                    ),
                  ],
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(30),
                  ),
                ),
                child: Stack(
                  children: [
                    // دوائر زخرفية متحركة في الـ AppBar
                    Positioned(
                      right: -40,
                      top: -30,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.1),
                        ),
                      ),
                    ),
                    Positioned(
                      left: -30,
                      bottom: -40,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.05),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة الاستكشاف مع تأثير جميل
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.25),
                      Colors.white.withValues(alpha: 0.15),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.explore_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              // النص مع تأثيرات جميلة
              Text(
                translations.translate('explore'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  letterSpacing: 0.5,
                  shadows: [
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
            ],
          ),
          centerTitle: true,
          leading: Container(
            margin: const EdgeInsets.only(right: 12, top: 8, bottom: 8),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.25),
                      Colors.white.withValues(alpha: 0.15),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.search_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      translations.translate('search_places'),
                      style: GoogleFonts.ibmPlexSansArabic(),
                      textAlign: TextAlign.center,
                    ),
                    backgroundColor: const Color(0xFF4F908E),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    margin: const EdgeInsets.all(16),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            ),
          ),
          actions: [
            Container(
              margin:
                  const EdgeInsets.only(left: 16, right: 8, top: 8, bottom: 8),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.25),
                      Colors.white.withValues(alpha: 0.15),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    'assets/images/wifada_logo.png',
                    width: 28,
                    height: 28,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
          ),
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(
                child: AnimatedBuilder(
                  animation: _floatingAnimationController,
                  builder: (context, child) {
                    return Transform.translate(
                        offset: Offset(
                            0,
                            8 *
                                math.sin(_floatingAnimationController.value *
                                    math.pi)),
                        child: Container(
                          margin: const EdgeInsets.fromLTRB(20, 24, 20, 0),
                          padding: const EdgeInsets.all(28),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF1A5F5D),
                                Color(0xFF2D7A77),
                                Color(0xFF4F908E),
                                Color(0xFF6BA8A6),
                              ],
                              stops: [0.0, 0.3, 0.7, 1.0],
                            ),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF2D7A77)
                                    .withValues(alpha: 0.4),
                                offset: const Offset(0, 12),
                                blurRadius: 25,
                                spreadRadius: 0,
                              ),
                              BoxShadow(
                                color: Colors.white.withValues(alpha: 0.1),
                                offset: const Offset(0, -2),
                                blurRadius: 10,
                                spreadRadius: 0,
                              ),
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                offset: const Offset(0, 4),
                                blurRadius: 8,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.white.withOpacity(0.2),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: const Icon(
                                            Icons.location_on,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Text(
                                          translations.translate('madinah'),
                                          style: GoogleFonts.ibmPlexSansArabic(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ).animate().fadeIn(
                                        delay:
                                            const Duration(milliseconds: 300)),
                                    const SizedBox(height: 16),
                                    Text(
                                      translations.translate('explore_madinah'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ).animate().fadeIn(
                                        delay:
                                            const Duration(milliseconds: 400)),
                                    const SizedBox(height: 8),
                                    Text(
                                      translations.translate(
                                          'discover_beautiful_places'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        fontSize: 14,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ).animate().fadeIn(
                                        delay:
                                            const Duration(milliseconds: 500)),
                                  ],
                                ),
                              ),
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.explore,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              )
                                  .animate()
                                  .fadeIn(
                                      delay: const Duration(milliseconds: 600))
                                  .scale(),
                            ],
                          ),
                        ));
                  },
                ),
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index >= sections.length) {
                      return const SizedBox(height: 50);
                    }

                    final section = sections[index];
                    final destinations = List<Map<String, dynamic>>.from(
                        section['destinations'] ?? []);

                    if (destinations.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return _buildSection(
                      title: section['title'] ?? '',
                      destinations: destinations,
                    );
                  },
                  childCount: sections.length + 1,
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildSection({
    required String title,
    required List<Map<String, dynamic>> destinations,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF4F908E),
                        Color(0xFF2D7A77),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF4F908E).withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 0,
                        offset: const Offset(0, 6),
                      ),
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.8),
                        blurRadius: 6,
                        spreadRadius: 0,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Icon(
                    _getSectionIcon(title),
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 14),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2D3142),
                    ),
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AllDestinationsScreen(
                            title: title,
                            destinations: destinations,
                          ),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4F908E).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Text(
                            translations.translate('view_all'),
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF4F908E),
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 14,
                            color: Color(0xFF4F908E),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 220,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: destinations.length,
              itemBuilder: (context, index) {
                return _buildDestinationCard(destinations[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard(Map<String, dynamic> destination, [int? index]) {
    return AnimatedBuilder(
      animation: _floatingAnimationController,
      builder: (context, child) {
        return Transform.translate(
            offset: Offset(
                0,
                3 *
                    math.sin((_floatingAnimationController.value * math.pi) +
                        (index ?? 0) * 0.5)),
            child: Container(
              margin: const EdgeInsets.only(right: 16),
              width: 220,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF2D7A77).withValues(alpha: 0.15),
                    offset: const Offset(0, 8),
                    blurRadius: 20,
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    offset: const Offset(0, 4),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.8),
                    offset: const Offset(0, -2),
                    blurRadius: 6,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            DestinationDetailsScreen(destination: destination),
                      ),
                    );
                  },
                  borderRadius: BorderRadius.circular(24),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: SizedBox(
                          height: 220,
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              Hero(
                                tag: 'destination_${destination['title']}',
                                child: CachedNetworkImage(
                                  imageUrl: destination['image'],
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) =>
                                      Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(color: Colors.white),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                    color: const Color(0xFF4F908E)
                                        .withValues(alpha: 0.1),
                                    child: const Icon(
                                      Icons.image,
                                      color: Color(0xFF4F908E),
                                      size: 40,
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      Colors.black.withValues(alpha: 0.8),
                                    ],
                                    stops: const [0.5, 1.0],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Positioned(
                      //   top: 12,
                      //   right: 12,
                      //   child: Container(
                      //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      //     decoration: BoxDecoration(
                      //       color: Colors.black.withOpacity(0.6),
                      //       borderRadius: BorderRadius.circular(20),
                      //     ),
                      //     child: Row(
                      //       mainAxisSize: MainAxisSize.min,
                      //       children: [
                      //         const Icon(
                      //           Icons.star,
                      //           color: Colors.amber,
                      //           size: 16,
                      //         ),
                      //         const SizedBox(width: 4),
                      //         Text(
                      //           '4.8',
                      //           style: GoogleFonts.ibmPlexSansArabic(
                      //             fontSize: 12,
                      //             fontWeight: FontWeight.bold,
                      //             color: Colors.white,
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                      Positioned(
                        bottom: 16,
                        left: 16,
                        right: 16,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              destination['title'],
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                height: 1.2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Icon(
                                    Icons.location_on,
                                    color: Colors.white,
                                    size: 14,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    destination['location'] ??
                                        translations.translate('madinah'),
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: 13,
                                      color: Colors.white,
                                      height: 1.2,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
      },
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'المسجد النبوي':
        return Icons.mosque;
      case 'مسجد قباء':
        return Icons.location_city;
      case 'المساجد الأثرية':
        return Icons.history;
      case 'آبار ومزارع':
        return Icons.nature_people;
      case 'مواقع تاريخية':
        return Icons.place;
      case 'متاحف ومعارض':
        return Icons.museum;
      case 'مماشي ومنتزهات':
        return Icons.park;
      case 'أسواق ومتاجر':
        return Icons.shopping_cart;
      default:
        return Icons.location_on;
    }
  }
}
