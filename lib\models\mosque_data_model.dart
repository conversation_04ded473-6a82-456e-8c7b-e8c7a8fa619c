class MosqueDataModel {
  final String id;
  final String name;
  final String description;
  final LiveStreamData liveStream;
  final List<GuidelineData> guidelines;
  final MapData mapData;
  final List<FacilityData> facilities;

  MosqueDataModel({
    required this.id,
    required this.name,
    required this.description,
    required this.liveStream,
    required this.guidelines,
    required this.mapData,
    required this.facilities,
  });

  factory MosqueDataModel.fromJson(Map<String, dynamic> json) {
    return MosqueDataModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      liveStream: LiveStreamData.fromJson(json['live_stream'] ?? {}),
      guidelines: (json['guidelines'] as List? ?? [])
          .map((guideline) => GuidelineData.fromJson(guideline))
          .toList(),
      mapData: MapData.fromJson(json['map_data'] ?? {}),
      facilities: (json['facilities'] as List? ?? [])
          .map((facility) => FacilityData.fromJson(facility))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'live_stream': liveStream.toJson(),
      'guidelines': guidelines.map((guideline) => guideline.toJson()).toList(),
      'map_data': mapData.toJson(),
      'facilities': facilities.map((facility) => facility.toJson()).toList(),
    };
  }
}

class LiveStreamData {
  final String videoId;
  final bool isLive;
  final String status;
  final String streamTime;
  final String quality;
  final String audioInfo;

  LiveStreamData({
    required this.videoId,
    required this.isLive,
    required this.status,
    required this.streamTime,
    required this.quality,
    required this.audioInfo,
  });

  factory LiveStreamData.fromJson(Map<String, dynamic> json) {
    return LiveStreamData(
      videoId: json['video_id'] ?? '',
      isLive: json['is_live'] ?? false,
      status: json['status'] ?? '',
      streamTime: json['stream_time'] ?? '',
      quality: json['quality'] ?? '',
      audioInfo: json['audio_info'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'video_id': videoId,
      'is_live': isLive,
      'status': status,
      'stream_time': streamTime,
      'quality': quality,
      'audio_info': audioInfo,
    };
  }
}

class GuidelineData {
  final String id;
  final String title;
  final String description;
  final String videoId;
  final List<StepData> steps;
  final List<String> duas;

  GuidelineData({
    required this.id,
    required this.title,
    required this.description,
    required this.videoId,
    required this.steps,
    required this.duas,
  });

  factory GuidelineData.fromJson(Map<String, dynamic> json) {
    return GuidelineData(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      videoId: json['video_id'] ?? '',
      steps: (json['steps'] as List? ?? [])
          .map((step) => StepData.fromJson(step))
          .toList(),
      duas: List<String>.from(json['duas'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'video_id': videoId,
      'steps': steps.map((step) => step.toJson()).toList(),
      'duas': duas,
    };
  }
}

class StepData {
  final int number;
  final String description;

  StepData({
    required this.number,
    required this.description,
  });

  factory StepData.fromJson(Map<String, dynamic> json) {
    return StepData(
      number: json['number'] ?? 0,
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'description': description,
    };
  }
}

class MapData {
  final String imageUrl;
  final String zoomHint;
  final List<MapMarker> markers;

  MapData({
    required this.imageUrl,
    required this.zoomHint,
    required this.markers,
  });

  factory MapData.fromJson(Map<String, dynamic> json) {
    return MapData(
      imageUrl: json['image_url'] ?? '',
      zoomHint: json['zoom_hint'] ?? '',
      markers: (json['markers'] as List? ?? [])
          .map((marker) => MapMarker.fromJson(marker))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'image_url': imageUrl,
      'zoom_hint': zoomHint,
      'markers': markers.map((marker) => marker.toJson()).toList(),
    };
  }
}

class MapMarker {
  final String id;
  final String title;
  final double x;
  final double y;
  final String description;

  MapMarker({
    required this.id,
    required this.title,
    required this.x,
    required this.y,
    required this.description,
  });

  factory MapMarker.fromJson(Map<String, dynamic> json) {
    return MapMarker(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      x: (json['x'] ?? 0.0).toDouble(),
      y: (json['y'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'x': x,
      'y': y,
      'description': description,
    };
  }
}

class FacilityData {
  final String id;
  final String title;
  final String description;
  final String iconName;
  final List<String> details;

  FacilityData({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.details,
  });

  factory FacilityData.fromJson(Map<String, dynamic> json) {
    return FacilityData(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      iconName: json['icon_name'] ?? '',
      details: List<String>.from(json['details'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon_name': iconName,
      'details': details,
    };
  }
}
