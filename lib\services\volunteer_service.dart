import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import '../models/volunteer_opportunity_model.dart';

/// خدمة للتعامل مع بيانات الفرص التطوعية
class VolunteerService {
  static const String _baseUrl = 'http://wefadaboard.kesug.com';
  static const String _opportunitiesEndpoint = '/get_opportunities';

  Future<List<dynamic>> fetchOpportunities() async {
    try {
      final response = await http.get(Uri.parse('$_baseUrl$_opportunitiesEndpoint'));

      if (response.statusCode == 200) {
        // تحويل البيانات من JSON إلى قائمة
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('فشل في تحميل الفرص التطوعية: ${response.statusCode}');
      }
    } catch (e) {
      // debugPrint('خطأ في جلب الفرص التطوعية: $e');
      rethrow;
    }
  }

  Map<String, List<VolunteerOpportunity>> convertWordPressData(List<dynamic> wordpressData) {
    final current = <VolunteerOpportunity>[];
    final upcoming = <VolunteerOpportunity>[];
    final past = <VolunteerOpportunity>[];

    for (final item in wordpressData) {
      // استخراج البيانات من post_content
      final postContent = item['post_content'] as Map<String, dynamic>? ?? {};

      // استخراج المتطلبات والمزايا
      List<String> requirementsList = [];
      if (postContent['requirements'] != null) {
        if (postContent['requirements'] is String) {
          requirementsList.add(postContent['requirements']);
        } else if (postContent['requirements'] is List) {
          requirementsList = List<String>.from(postContent['requirements']);
        }
      }

      List<String> benefitsList = [];
      if (postContent['features'] != null) {
        if (postContent['features'] is String) {
          benefitsList.add(postContent['features']);
        } else if (postContent['features'] is List) {
          benefitsList = List<String>.from(postContent['features']);
        }
      }

      // تحديد الحالة بناءً على التاريخ
      String status = 'متاح';
      try {
        final opportunityDate = DateTime.parse(postContent['date'] ?? '2099-01-01');
        final now = DateTime.now();
        if (opportunityDate.isBefore(now)) {
          status = 'منتهي';
        } else if (opportunityDate.difference(now).inDays > 30) {
          status = 'قريباً';
        }
      } catch (e) {
        // إذا كان التاريخ بتنسيق غير قياسي، نستخدم الحالة الافتراضية
        status = 'متاح';
      }

      // إنشاء نموذج VolunteerOpportunity
      final opportunity = VolunteerOpportunity(
        id: item['ID']?.toString() ?? '0',
        title: item['post_title'] ?? 'فرصة تطوعية',
        organization: postContent['organization'] ?? 'جمعية وِفادة التطوعية',
        description: postContent['description'] ?? 'لا يوجد وصف متاح',
        location: postContent['location'] ?? 'المدينة المنورة',
        date: postContent['date'] ?? 'قريباً',
        time: postContent['time'] ?? 'يحدد لاحقاً',
        seats: postContent['available_seats'] is int
            ? postContent['available_seats']
            : int.tryParse(postContent['available_seats']?.toString() ?? '0') ?? 0,
        imageUrl: 'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
        requirements: requirementsList.isNotEmpty ? requirementsList : ['لا توجد متطلبات محددة'],
        benefits: benefitsList.isNotEmpty ? benefitsList : ['شهادة تطوع معتمدة'],
        status: status,
        registerUrl: postContent['registerUrl'] ?? '' ?? 'لايوجد رابط للتسجيل',
      );

      // تصنيف الفرصة التطوعية
      if (status == 'متاح') {
        current.add(opportunity);
      } else if (status == 'قريباً') {
        upcoming.add(opportunity);
      } else if (status == 'منتهي') {
        past.add(opportunity);
      } else {
        // إذا كانت الحالة غير معروفة، نضعها في الفرص الحالية
        current.add(opportunity);
      }
    }

    return {
      'current': current,
      'upcoming': upcoming,
      'past': past,
    };
  }

  /// التسجيل في فرصة تطوعية - يفتح رابط موقع التسجيل
  Future<bool> registerForOpportunity(String opportunityId, String userId) async {
    try {
      // البحث عن الفرصة التطوعية في البيانات المحلية
      final opportunities = await fetchOpportunities();

      // البحث عن الفرصة التطوعية بواسطة المعرف
      final opportunity = opportunities.firstWhere(
        (item) => item['ID'].toString() == opportunityId,
        orElse: () => null,
      );

      if (opportunity != null) {
        // استخراج البيانات من post_content
        final postContent = opportunity['post_content'] as Map<String, dynamic>? ?? {};

        // استخدام رابط التسجيل من البيانات أو الرابط الافتراضي
        String registerUrl = postContent['registerUrl'];
        // إضافة معلومات الفرصة التطوعية إلى الرابط
        registerUrl += '?opportunity_id=$opportunityId';
        registerUrl += '&title=${Uri.encodeComponent(opportunity['post_title'] ?? '')}';
        registerUrl += '&organization=${Uri.encodeComponent(postContent['organization'] ?? '')}';
        registerUrl += '&location=${Uri.encodeComponent(postContent['location'] ?? '')}';
        registerUrl += '&date=${Uri.encodeComponent(postContent['date'] ?? '')}';
        registerUrl += '&user_id=$userId';

        // فتح الرابط في المتصفح
        debugPrint('فتح رابط التسجيل: $registerUrl');

        // فتح الرابط في المتصفح
        final Uri uri = Uri.parse(registerUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          debugPrint('لا يمكن فتح الرابط: $registerUrl');
        }

        return true;
      } else {
        debugPrint('لم يتم العثور على الفرصة التطوعية بالمعرف: $opportunityId');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في التسجيل: $e');
      return false;
    }
  }
}
