{"dart.flutterSdkPath": null, "dart.analysisExcludedFolders": ["build", ".dart_tool", "android/.gradle", "ios/build", "windows/build", "linux/build", "macos/build"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.dart_tool": true, "**/build": true, "**/*.lock": true, "android/.gradle": true, "android/app/build": true, "ios/build": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/.dart_tool": true, "**/build": true, "android/.gradle": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.dart_tool/**": true, "**/build/**": true, "android/.gradle/**": true}, "dart.buildRunnerAdditionalArgs": ["--delete-conflicting-outputs"], "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true}