import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../models/event.dart';
import '../../providers/events_provider.dart';
import 'event_details_page.dart';

class EventsListPage extends StatefulWidget {
  const EventsListPage({super.key});

  @override
  State<EventsListPage> createState() => _EventsListPageState();
}

class _EventsListPageState extends State<EventsListPage> {
  String _selectedCategory = 'الكل';
  String _selectedStatus = 'الكل';
  final TextEditingController _searchController = TextEditingController();

  final List<String> _categories = [
    'الكل',
    'معارض',
    'مهرجانات',
    'فنون',
    'تقنية',
    'طعام',
    'رياضة',
    'حرف يدوية',
    'مؤتمرات',
    'ثقافة'
  ];

  final List<String> _statuses = [
    'الكل',
    'قريباً',
    'جارٍ الآن',
    'منتهي'
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<EventsProvider>().fetchEvents();
    });

    _searchController.addListener(() {
      setState(() {
        // تحديث القائمة عند تغيير نص البحث
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F908E).withOpacity(0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: Consumer<EventsProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: Color(0xFF4F908E),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل الفعاليات...',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                  ],
                ),
              );
            }

            if (provider.error.isNotEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Color(0xFF4F908E),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'حدث خطأ في تحميل البيانات',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () => provider.fetchEvents(),
                      icon: const Icon(Icons.refresh, color: Color(0xFF4F908E)),
                      label: Text(
                        'إعادة المحاولة',
                        style: GoogleFonts.ibmPlexSansArabic(
                          color: const Color(0xFF4F908E),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildSliverAppBar(),
                _buildCategoriesBar(),
                _buildEventsList(),
                // إضافة مساحة في الأسفل
                const SliverToBoxAdapter(
                  child: SizedBox(height: 24),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: const Color(0xFF4F908E),
      elevation: 0,
      pinned: true,
      expandedHeight: 180,
      flexibleSpace: FlexibleSpaceBar(
        titlePadding: const EdgeInsets.only(bottom: 62),
        title: Text(
          'الفعاليات والأنشطة',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                const Color(0xFF4F908E),
                const Color(0xFF307371),
              ],
            ),
          ),
          child: Stack(
            children: [
              // زخرفة الخلفية
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.05),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: 0,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 16),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.filter_list, color: Colors.white, size: 16),
          ),
          onPressed: () {
            // يمكن إضافة وظيفة فلترة متقدمة هنا
          },
        ),
        const SizedBox(width: 8),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TextField(
            controller: _searchController,
            style: GoogleFonts.ibmPlexSansArabic(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'ابحث عن فعالية...',
              hintStyle: GoogleFonts.ibmPlexSansArabic(color: Colors.white70),
              prefixIcon: const Icon(Icons.search, color: Colors.white70),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.white70),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.white.withOpacity(0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 0),
            ),
            textAlignVertical: TextAlignVertical.center,
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesBar() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          // فلتر الفئات
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    selected: isSelected,
                    label: Text(category),
                    onSelected: (selected) {
                      setState(() => _selectedCategory = category);
                    },
                    backgroundColor: Colors.white,
                    selectedColor: const Color(0xFF4F908E).withOpacity(0.2),
                    labelStyle: GoogleFonts.ibmPlexSansArabic(
                      color: isSelected ? const Color(0xFF4F908E) : Colors.black87,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(
                        color: isSelected ? const Color(0xFF4F908E) : Colors.grey[300]!,
                      ),
                    ),
                    avatar: isSelected
                        ? const Icon(Icons.check_circle, size: 16, color: Color(0xFF4F908E))
                        : null,
                  ),
                );
              },
            ),
          ),

          // فلتر الحالة
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _statuses.length,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                final status = _statuses[index];
                final isSelected = status == _selectedStatus;

                // تحديد لون الفلتر حسب الحالة
                Color chipColor;
                if (status == 'قريباً') {
                  chipColor = const Color(0xFF4F908E);
                } else if (status == 'جارٍ الآن') {
                  chipColor = Colors.orange;
                } else if (status == 'منتهي') {
                  chipColor = Colors.grey;
                } else {
                  chipColor = Colors.black87;
                }

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    selected: isSelected,
                    label: Text(status),
                    onSelected: (selected) {
                      setState(() => _selectedStatus = status);
                    },
                    backgroundColor: Colors.white,
                    selectedColor: chipColor.withOpacity(0.2),
                    labelStyle: GoogleFonts.ibmPlexSansArabic(
                      color: isSelected ? chipColor : Colors.black87,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(
                        color: isSelected ? chipColor : Colors.grey[300]!,
                      ),
                    ),
                    avatar: isSelected
                        ? Icon(Icons.check_circle, size: 16, color: chipColor)
                        : null,
                  ),
                );
              },
            ),
          ),

          // عدد النتائج
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'النتائج: ${_getFilteredEvents().length}',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedCategory = 'الكل';
                      _selectedStatus = 'الكل';
                      _searchController.clear();
                    });
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label: Text(
                    'إعادة ضبط الفلاتر',
                    style: GoogleFonts.ibmPlexSansArabic(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Event> _getFilteredEvents() {
    final allEvents = context.read<EventsProvider>().events;

    // تطبيق فلتر الفئة
    var filteredEvents = _selectedCategory == 'الكل'
        ? allEvents
        : allEvents.where((event) => event.category == _selectedCategory).toList();

    // تطبيق فلتر الحالة
    if (_selectedStatus != 'الكل') {
      String statusFilter;
      switch (_selectedStatus) {
        case 'قريباً':
          statusFilter = 'upcoming';
          break;
        case 'جارٍ الآن':
          statusFilter = 'ongoing';
          break;
        case 'منتهي':
          statusFilter = 'completed';
          break;
        default:
          statusFilter = '';
      }

      if (statusFilter.isNotEmpty) {
        filteredEvents = filteredEvents.where((event) => event.status == statusFilter).toList();
      }
    }

    // تطبيق البحث
    if (_searchController.text.isNotEmpty) {
      final searchQuery = _searchController.text.toLowerCase();
      filteredEvents = filteredEvents.where((event) {
        return event.title.toLowerCase().contains(searchQuery) ||
               event.description.toLowerCase().contains(searchQuery) ||
               event.location.toLowerCase().contains(searchQuery) ||
               event.organizationName.toLowerCase().contains(searchQuery);
      }).toList();
    }

    return filteredEvents;
  }

  Widget _buildEventsList() {
    final filteredEvents = _getFilteredEvents();

    if (filteredEvents.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.event_busy, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'لا توجد فعاليات مطابقة للبحث',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'حاول تغيير معايير البحث أو الفلاتر',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final event = filteredEvents[index];
          return _buildEventCard(event);
        },
        childCount: filteredEvents.length,
      ),
    );
  }

  Widget _buildEventCard(Event event) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EventDetailsPage(event: event),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // صورة الحدث
              Stack(
                children: [
                  // الصورة
                  SizedBox(
                    height: 200,
                    child: Hero(
                      tag: 'event_image_${event.id}',
                      child: Image.network(
                        event.imageUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 200,
                            color: const Color(0xFFF5F7FA),
                            child: Icon(event.icon, size: 60, color: Colors.grey[400]),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            height: 200,
                            color: const Color(0xFFF5F7FA),
                            child: Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                                color: const Color(0xFF2A3F54),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // تدرج لون فوق الصورة
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // حالة الحدث
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(event.status),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getStatusIcon(event.status),
                            size: 14,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _getStatusText(event.status),
                            style: GoogleFonts.ibmPlexSansArabic(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // فئة الحدث
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            event.icon,
                            size: 14,
                            color: const Color(0xFF4F908E),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            event.category,
                            style: GoogleFonts.ibmPlexSansArabic(
                              color: const Color(0xFF4F908E),
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // عنوان الحدث
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        event.title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              offset: const Offset(0, 2),
                              blurRadius: 4,
                              color: Colors.black.withOpacity(0.5),
                            ),
                          ],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),

              // معلومات الحدث
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(
                    bottom: Radius.circular(20),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الموقع والتاريخ
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F7FA),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildInfoRow(
                                  Icons.location_on,
                                  event.location,
                                  const Color(0xFF2A3F54),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildInfoRow(
                                  Icons.calendar_today,
                                  event.date,
                                  const Color(0xFF2A3F54),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildInfoRow(
                                  Icons.business,
                                  event.organizationName,
                                  const Color(0xFF2A3F54),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildInfoRow(
                                  Icons.access_time,
                                  event.time,
                                  const Color(0xFF2A3F54),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // المميزات
                    if (event.features.isNotEmpty) ...[
                      Text(
                        'المميزات',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF4F908E),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: event.features.take(3).map((feature) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4F908E).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(
                                color: const Color(0xFF4F908E).withOpacity(0.2),
                              ),
                            ),
                            child: Text(
                              feature,
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 13,
                                color: const Color(0xFF4F908E),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // زر عرض التفاصيل
                    Center(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => EventDetailsPage(event: event),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4F908E),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'عرض التفاصيل',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'upcoming':
        return Icons.event_available;
      case 'ongoing':
        return Icons.event_note;
      case 'completed':
        return Icons.event_busy;
      default:
        return Icons.event;
    }
  }

  Widget _buildInfoRow(IconData icon, String text, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: const Color(0xFF4F908E)),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'upcoming':
        return const Color(0xFF4F908E);
      case 'ongoing':
        return const Color(0xFFD2AF53);
      case 'completed':
        return const Color(0xFF9A9A9A);
      default:
        return const Color(0xFF9A9A9A);
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'upcoming':
        return 'قريباً';
      case 'ongoing':
        return 'جارٍ الآن';
      case 'completed':
        return 'منتهي';
      default:
        return '';
    }
  }
}
