import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../../providers/mosque_provider.dart';
import '../../../models/mosque_data_model.dart';
import 'video_fullscreen_page.dart'; // استيراد صفحة عرض الفيديو بملء الشاشة

class GuidelinesTab extends StatefulWidget {
  const GuidelinesTab({super.key});

  @override
  State<GuidelinesTab> createState() => _GuidelinesTabState();
}

class _GuidelinesTabState extends State<GuidelinesTab> {
  bool _isLoading = true;
  List<GuidelineData> _guidelines = [];

  @override
  void initState() {
    super.initState();
    _loadGuidelinesData();
  }

  // جلب بيانات الإرشادات من المزود
  Future<void> _loadGuidelinesData() async {
    try {
      final mosqueProvider = Provider.of<MosqueProvider>(context, listen: false);

      // انتظار حتى تكون البيانات جاهزة
      if (mosqueProvider.guidelinesData.isEmpty) {
        await Future.delayed(const Duration(milliseconds: 500));
        if (!mounted) return;
        return _loadGuidelinesData();
      }

      // تحديث البيانات
      setState(() {
        _guidelines = mosqueProvider.guidelinesData;
        _isLoading = false;
      });
    } catch (e) {
      // استخدام البيانات الافتراضية في حالة حدوث خطأ
      setState(() {
        _isLoading = false;
      });
      print('خطأ في تحميل بيانات الإرشادات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض مؤشر التحميل
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF4F908E),
        ),
      );
    }

    // استخدام SizedBox مع ListView لتوحيد طريقة العرض مع باقي التبويبات
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        itemCount: _guidelines.length,
        itemBuilder: (context, index) {
          final guideline = _guidelines[index];
          return _buildGuidelineCard(context, guideline);
        },
      ),
    );
  }

  Widget _buildGuidelineCard(BuildContext context, GuidelineData guideline) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF4F908E).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getIconForGuideline(guideline.id),
            color: const Color(0xFF4F908E),
          ),
        ),
        title: Text(
          guideline.title,
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          guideline.description,
          style: GoogleFonts.ibmPlexSansArabic(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        children: [
          if (guideline.videoId.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    YoutubePlayerBuilder(
                      player: YoutubePlayer(
                        controller: YoutubePlayerController(
                          initialVideoId: guideline.videoId,
                          flags: const YoutubePlayerFlags(
                            autoPlay: false,
                            mute: false,
                            hideControls: false,
                            controlsVisibleAtStart: true,
                          ),
                        ),
                        showVideoProgressIndicator: true,
                        progressIndicatorColor: const Color(0xFF4F908E),
                      ),
                      builder: (context, player) => player,
                    ),
                    // زر التكبير
                    Positioned(
                      bottom: 10,
                      right: 10,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 8,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.fullscreen, color: Colors.white),
                          onPressed: () {
                            // فتح صفحة جديدة لعرض الفيديو بملء الشاشة
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => VideoFullscreenPage(
                                  videoId: guideline.videoId,
                                  autoPlay: true,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الخطوات:',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF4F908E),
                  ),
                ),
                const SizedBox(height: 12),
                ...guideline.steps.map((step) => _buildStepItem(step.description)),
                if (guideline.duas.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'الدعاء:',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF4F908E),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4F908E).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      guideline.duas.first, // استخدام أول دعاء في القائمة
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        height: 1.6,
                        color: const Color(0xFF2D3142),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة للحصول على الأيقونة المناسبة للإرشاد
  IconData _getIconForGuideline(String guidelineId) {
    switch (guidelineId) {
      case 'funeral_prayer':
        return Icons.people;
      case 'grave_visit':
        return Icons.star;
      default:
        return Icons.mosque;
    }
  }

  Widget _buildStepItem(String step) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            height: 8,
            width: 8,
            decoration: const BoxDecoration(
              color: Color(0xFF4F908E),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              step,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 15,
                height: 1.5,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
