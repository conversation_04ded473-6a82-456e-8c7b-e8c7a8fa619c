import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';

class LanguageSettingsScreen extends StatelessWidget {
  const LanguageSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          AppLocalizations(languageProvider.currentLanguage).translate('language'),
          style: GoogleFonts.notoKufiArabic(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        color: Colors.white,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: AppLocalizations.supportedLanguages.length,
          itemBuilder: (context, index) {
            final language = AppLocalizations.supportedLanguages[index];
            final isSelected = language['code'] == languageProvider.currentLanguage;
        
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListTile(
                onTap: () => languageProvider.changeLanguage(language['code']!),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                title: Text(
                  language['nativeName']!,
                  style: GoogleFonts.notoKufiArabic(
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? const Color(0xFF4F908E) : Colors.black87,
                  ),
                ),
                subtitle: Text(
                  language['name']!,
                  style: GoogleFonts.notoKufiArabic(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                trailing: isSelected
                    ? const Icon(
                        Icons.check_circle,
                        color: Color(0xFF4F908E),
                      )
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }
}
