# قواعد ProGuard لتحسين حجم التطبيق

# الاحتفاظ بـ Flutter
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# الاحتفاظ بـ Google Maps
-keep class com.google.android.gms.maps.** { *; }
-keep interface com.google.android.gms.maps.** { *; }

# الاحتفاظ بـ WebView
-keep class android.webkit.** { *; }

# الاحتفاظ بـ JSON
-keepattributes *Annotation*
-keepclassmembers class ** {
    @com.google.gson.annotations.SerializedName <fields>;
}

# الاحتفاظ بـ Reflection
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# تحسين الكود
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# إزالة التحذيرات
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn kotlin.jvm.internal.**
