import 'package:flutter/foundation.dart';
import 'package:wiffada/models/place.dart';
import 'package:wiffada/services/places_service.dart';


class PlacesProvider with ChangeNotifier {
  final PlacesService _placesService;
  List<Place> _places = [];
  bool _isLoading = false;
  String _error = '';
  String _selectedCategory = 'الكل';

  PlacesProvider({PlacesService? placesService})
      : _placesService = placesService ?? PlacesService();

  List<Place> get places => _places;
  bool get isLoading => _isLoading;
  String get error => _error;
  String get selectedCategory => _selectedCategory;

  Future<void> fetchPlaces() async {
    _setLoading(true);
    try {
      _places = await _placesService.getPlaces();
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> filterByCategory(String category) async {
    if (_selectedCategory == category) return;
    
    _setLoading(true);
    _selectedCategory = category;
    
    try {
      _places = await _placesService.getPlacesByCategory(category);
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
}
