# تحسين أداء Gradle على مستوى المشروع

# تحسين استخدام الذاكرة
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# تفعيل البناء المتوازي
org.gradle.parallel=true

# تفعيل التخزين المؤقت
org.gradle.caching=true

# تفعيل التكوين عند الطلب
org.gradle.configureondemand=true

# تفعيل Gradle Daemon
org.gradle.daemon=true

# تحسين Kotlin
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.caching.enabled=true

# تحسين Android
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
android.enableBuildCache=true
