import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/prayer_provider.dart';

class PrayerTimesWidget extends StatelessWidget {
  const PrayerTimesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<PrayerProvider>(
      builder: (context, provider, child) {
        final prayerTimes = provider.prayerTimes;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مواقيت الصلاة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                _buildPrayerTime('الفجر', provider.formatTime24Hour(prayerTimes?.fajr)),
                _buildPrayerTime('الشروق', provider.formatTime24Hour(prayerTimes?.sunrise)),
                _buildPrayerTime('الظهر', provider.formatTime24Hour(prayerTimes?.dhuhr)),
                _buildPrayerTime('العصر', provider.formatTime24Hour(prayerTimes?.asr)),
                _buildPrayerTime('المغرب', provider.formatTime24Hour(prayerTimes?.maghrib)),
                _buildPrayerTime('العشاء', provider.formatTime24Hour(prayerTimes?.isha)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPrayerTime(String name, String time) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(name),
          Text(time),
        ],
      ),
    );
  }
}
