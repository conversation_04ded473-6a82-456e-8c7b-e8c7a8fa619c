import 'package:flutter/material.dart';
import '../models/volunteer_opportunity_model.dart';
import '../services/volunteer_service.dart';

enum VolunteerStatus {
  initial,
  loading,
  loaded,
  error,
}

class VolunteerProvider extends ChangeNotifier {
  List<VolunteerOpportunity> _currentOpportunities = [];
  List<VolunteerOpportunity> _upcomingOpportunities = [];
  List<VolunteerOpportunity> _pastOpportunities = [];
  VolunteerStatus _status = VolunteerStatus.initial;
  String _errorMessage = '';

  final VolunteerService _volunteerService = VolunteerService();

  List<VolunteerOpportunity> get currentOpportunities => _currentOpportunities;
  List<VolunteerOpportunity> get upcomingOpportunities =>
      _upcomingOpportunities;
  List<VolunteerOpportunity> get pastOpportunities => _pastOpportunities;
  VolunteerStatus get status => _status;
  String get errorMessage => _errorMessage;

  VolunteerProvider() {
    fetchOpportunities();
  }

  Future<void> fetchOpportunities() async {
    try {
      _status = VolunteerStatus.loading;
      notifyListeners();

      // استخدام خدمة المتطوعين لجلب البيانات
      final jsonData = await _volunteerService.fetchOpportunities();

      // تحويل البيانات إلى نموذج VolunteerOpportunity وتصنيفها
      final opportunities = _volunteerService.convertWordPressData(jsonData);

      // تحديث البيانات
      _currentOpportunities = opportunities['current'] ?? [];
      _upcomingOpportunities = opportunities['upcoming'] ?? [];
      _pastOpportunities = opportunities['past'] ?? [];

      _status = VolunteerStatus.loaded;
      notifyListeners();
    } catch (e) {
      _status = VolunteerStatus.error;
      _errorMessage = e.toString();
      notifyListeners();

  
      try {
        final jsonData = await _loadMockData();

        _currentOpportunities = _parseOpportunities(jsonData['current'] ?? []);
        _upcomingOpportunities = _parseOpportunities(jsonData['upcoming'] ?? []);
        _pastOpportunities = _parseOpportunities(jsonData['past'] ?? []);

        _status = VolunteerStatus.loaded;
        notifyListeners();
      } catch (innerError) {
        // إذا فشلت محاولة استخدام البيانات المحلية، نترك الحالة كما هي (خطأ)
        debugPrint('فشل في تحميل البيانات المحلية: $innerError');
      }
    }
  }

  Future<Map<String, dynamic>> _loadMockData() async {
    // في الإنتاج، يمكن استبدال هذا بطلب HTTP حقيقي
    return {
      'current': [
        {
          'id': '1',
          'title': 'مرشد للزوار في المسجد النبوي',
          'organization': 'وكالة شؤون المسجد النبوي',
          'description':
              'نبحث عن متطوعين للعمل كمرشدين للزوار في المسجد النبوي، يقومون بمساعدة الزوار وتوجيههم وتقديم المعلومات اللازمة.',
          'location': 'المسجد النبوي',
          'date': '1-30 رمضان 1445',
          'time': '4 ساعات يومياً',
          'seats': 50,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'إجادة اللغة العربية',
            'القدرة على التواصل الفعال',
            'المعرفة بتاريخ المسجد النبوي',
          ],
          'benefits': [
            'شهادة خبرة معتمدة',
            'زي رسمي',
            'تدريب متخصص',
          ],
          'status': 'متاح',
          'registerUrl': 'https://volunteer.sa',
        },
        {
          'id': '2',
          'title': 'متطوع في خدمة ضيوف الرحمن',
          'organization': 'جمعية وِفادة التطوعية',
          'description':
              'فرصة تطوعية لخدمة ضيوف الرحمن في المدينة المنورة، تشمل المساعدة في التنقل والإرشاد والترجمة.',
          'location': 'المنطقة المركزية',
          'date': '1-15 ذو الحجة 1445',
          'time': '6 ساعات يومياً',
          'seats': 100,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'حسن السيرة والسلوك',
            'القدرة على العمل ضمن فريق',
            'الالتزام بالمواعيد',
          ],
          'benefits': [
            'ساعات تطوع معتمدة',
            'دورات تدريبية',
            'مكافآت تحفيزية',
          ],
          'status': 'متاح',
          'registerUrl': 'https://volunteer.sa',
        },
        {
          'id': '3',
          'title': 'مساعد في تنظيم الزيارات',
          'organization': 'هيئة تطوير المدينة المنورة',
          'description':
              'المساعدة في تنظيم زيارات المعالم التاريخية والإسلامية في المدينة المنورة وتقديم المعلومات للزوار.',
          'location': 'المعالم التاريخية',
          'date': '1-30 شوال 1445',
          'time': '5 ساعات يومياً',
          'seats': 30,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'معرفة بالمعالم التاريخية',
            'مهارات تواصل جيدة',
            'القدرة على العمل لساعات طويلة',
          ],
          'benefits': [
            'شهادة تطوع',
            'تدريب على المهارات',
            'فرص تطوير مهني',
          ],
          'status': 'متاح',
          'registerUrl': 'https://volunteer.sa',
        },
      ],
      'upcoming': [
        {
          'id': '5',
          'title': 'برنامج خدمة المعتمرين',
          'organization': 'الهيئة العامة للعمرة والزيارة',
          'description':
              'فرصة تطوعية للمساعدة في تنظيم وتسهيل رحلة المعتمرين في المدينة المنورة',
          'location': 'المدينة المنورة',
          'date': 'محرم 1446',
          'time': '5 ساعات يومياً',
          'seats': 200,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'إجادة اللغة العربية',
            'القدرة على التواصل الفعال',
            'المعرفة بمناسك العمرة',
          ],
          'benefits': [
            'شهادة خبرة معتمدة',
            'زي رسمي',
            'تدريب متخصص',
          ],
          'status': 'قريباً',
          'registerUrl': 'https://volunteer.sa',
        },
        {
          'id': '6',
          'title': 'مرشد سياحي في المدينة',
          'organization': 'هيئة السياحة',
          'description':
              'فرصة تطوعية للعمل كمرشد سياحي في المدينة المنورة وتقديم المعلومات للزوار',
          'location': 'المدينة المنورة',
          'date': 'صفر 1446',
          'time': '6 ساعات يومياً',
          'seats': 50,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'إجادة اللغة الإنجليزية',
            'معرفة بتاريخ المدينة',
            'مهارات تواصل جيدة',
          ],
          'benefits': [
            'شهادة تطوع',
            'تدريب متخصص',
            'بدل مواصلات',
          ],
          'status': 'قريباً',
          'registerUrl': 'https://volunteer.sa',
        },
      ],
      'past': [
        {
          'id': '7',
          'title': 'مساعد في توزيع السحور',
          'organization': 'جمعية البر الخيرية',
          'description': 'توزيع وجبات السحور على المصلين في المسجد النبوي',
          'location': 'المسجد النبوي',
          'date': 'رمضان 1444',
          'time': '3 ساعات يومياً',
          'seats': 150,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'القدرة على العمل في ساعات متأخرة',
            'حسن التعامل مع الآخرين',
            'الالتزام بالمواعيد',
          ],
          'benefits': [
            'شهادة تطوع',
            'وجبات مجانية',
            'تجربة فريدة',
          ],
          'status': 'منتهي',
          'registerUrl': 'https://volunteer.sa',
        },
        {
          'id': '8',
          'title': 'مساعد في تنظيم الزوار',
          'organization': 'وكالة شؤون المسجد النبوي',
          'description': 'المساعدة في تنظيم دخول وخروج الزوار من المسجد النبوي',
          'location': 'المسجد النبوي',
          'date': 'ذو الحجة 1444',
          'time': '4 ساعات يومياً',
          'seats': 200,
          'imageUrl':
              'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
          'requirements': [
            'القدرة على العمل تحت الضغط',
            'مهارات تنظيمية جيدة',
            'اللياقة البدنية',
          ],
          'benefits': [
            'شهادة تطوع',
            'زي رسمي',
            'وجبات مجانية',
          ],
          'status': 'منتهي',
          'registerUrl': 'https://volunteer.sa',
        },
      ],
    };
  }

  List<VolunteerOpportunity> _parseOpportunities(List<dynamic> data) {
    return data.map((item) => VolunteerOpportunity.fromJson(item)).toList();
  }

  // تحديث البيانات من الخادم
  Future<void> refreshOpportunities() async {
    try {
      _status = VolunteerStatus.loading;
      notifyListeners();

      // استخدام خدمة المتطوعين لجلب البيانات
      final jsonData = await _volunteerService.fetchOpportunities();

      // تحويل البيانات إلى نموذج VolunteerOpportunity وتصنيفها
      final opportunities = _volunteerService.convertWordPressData(jsonData);

      // تحديث البيانات
      _currentOpportunities = opportunities['current'] ?? [];
      _upcomingOpportunities = opportunities['upcoming'] ?? [];
      _pastOpportunities = opportunities['past'] ?? [];

      _status = VolunteerStatus.loaded;
      notifyListeners();
    } catch (e) {
      _status = VolunteerStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // التسجيل في فرصة تطوعية
  Future<bool> registerForOpportunity(String opportunityId) async {
    try {
      // استخدام خدمة المتطوعين للتسجيل في الفرصة التطوعية
      return await _volunteerService.registerForOpportunity(
        opportunityId,
        'current_user_id', // يمكن استبداله بمعرف المستخدم الفعلي
      );
    } catch (e) {
      debugPrint('خطأ في التسجيل: $e');
      return false;
    }
  }
}
