import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/theme/app_theme.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:provider/provider.dart';

class BuildVirtualToursSection extends StatelessWidget {
  const BuildVirtualToursSection({super.key});

  @override
  Widget build(BuildContext context) {
        final colors = AppTheme.colors;
    final decorations = AppTheme.decorations;
    final translations = AppLocalizations(Provider.of<LanguageProvider>(context).currentLanguage);
      return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: GestureDetector(
        onTap: () => Navigator.pushNamed(context, '/virtual_tours'),
        child: Container(
          height: 180,
          decoration: BoxDecoration(
            color: colors.surface,
            borderRadius: BorderRadius.circular(25),
            boxShadow: decorations.cardShadow,
            image: const DecorationImage(
              image: AssetImage('assets/images/backgrounds/vir.jpg'),
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      Colors.transparent,
                      const Color(0xFF4F908E).withOpacity(0.95),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.view_in_ar,
                                      color: Colors.white.withOpacity(0.9),
                                      size: 16,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      translations.translate('unique_experience'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      translations.translate('virtual_tours'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      translations.translate('virtual_tours_subtitle'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        fontSize: 12,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      translations.translate('start_tour'),
                                      style: GoogleFonts.ibmPlexSansArabic(
                                        color: const Color(0xFF4F908E),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    const Icon(
                                      Icons.arrow_forward_ios,
                                      color: Color(0xFF4F908E),
                                      size: 14,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Expanded(child: SizedBox()),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}