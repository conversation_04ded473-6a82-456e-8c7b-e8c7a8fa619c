import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../../providers/volunteer_provider.dart';
import '../../models/volunteer_opportunity_model.dart';
import 'volunteer_details_page.dart';

class VolunteerPage extends StatefulWidget {
  const VolunteerPage({super.key});

  @override
  State<VolunteerPage> createState() => _VolunteerPageState();
}

class _VolunteerPageState extends State<VolunteerPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late VolunteerProvider _volunteerProvider;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _volunteerProvider = Provider.of<VolunteerProvider>(context, listen: false);
      _volunteerProvider.fetchOpportunities();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refreshData() async {
    await Provider.of<VolunteerProvider>(context, listen: false).refreshOpportunities();
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'متاح':
        return const Color(0xFF4F908E);
      case 'قريباً':
        return const Color(0xFFFFA000);
      case 'منتهي':
        return Colors.grey;
      default:
        return const Color(0xFF4F908E);
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'متاح':
        return Icons.check_circle_outline;
      case 'قريباً':
        return Icons.update;
      case 'منتهي':
        return Icons.event_busy;
      default:
        return Icons.check_circle_outline;
    }
  }

  Widget _buildShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 150,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
      ),
    );
  }

  Widget _buildNetworkImage(String? imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl ?? 'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
      height: 150,
      width: double.infinity,
      fit: BoxFit.cover,
      placeholder: (context, url) => _buildShimmerLoading(),
      errorWidget: (context, url, error) => Container(
        height: 150,
        color: Colors.grey[200],
        child: Icon(
          Icons.error_outline,
          color: Colors.grey[400],
          size: 40,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => VolunteerProvider(),
      child: Consumer<VolunteerProvider>(
        builder: (context, volunteerProvider, child) {
          return Scaffold(
            backgroundColor: Colors.grey[100],
            body: RefreshIndicator(
              onRefresh: _refreshData,
              child: NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) => [
                  SliverAppBar(
                    expandedHeight: 200,
                    floating: false,
                    pinned: true,
                    elevation: 0,
                    backgroundColor: const Color(0xFF4F908E),
                    flexibleSpace: FlexibleSpaceBar(
                      background: Stack(
                        children: [
                          Positioned.fill(
                            child: CachedNetworkImage(
                              imageUrl: 'https://static.srpcdigital.com/styles/1037xauto/public/2021/07/08/85684697964795705780870.jpg.webp',
                              fit: BoxFit.cover,
                              color: Colors.black.withOpacity(0.3),
                              colorBlendMode: BlendMode.darken,
                              placeholder: (context, url) => _buildShimmerLoading(),
                              errorWidget: (context, url, error) => Container(
                                color: const Color(0xFF4F908E),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 70,
                            left: 20,
                            right: 20,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'فرص التطوع',
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'كن جزءاً من خدمة زوار المدينة المنورة',
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 16,
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    bottom: PreferredSize(
                      preferredSize: const Size.fromHeight(48),
                      child: Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
                        ),
                        child: TabBar(
                          controller: _tabController,
                          labelColor: const Color(0xFF4F908E),
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: const Color(0xFF4F908E),
                          indicatorSize: TabBarIndicatorSize.label,
                          labelStyle: GoogleFonts.ibmPlexSansArabic(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          tabs: const [
                            Tab(text: 'الفرص الحالية'),
                            Tab(text: 'الفرص القادمة'),
                            Tab(text: 'الفرص السابقة'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
                body: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOpportunityListWithProvider(volunteerProvider.currentOpportunities, volunteerProvider.status),
                    _buildOpportunityListWithProvider(volunteerProvider.upcomingOpportunities, volunteerProvider.status),
                    _buildOpportunityListWithProvider(volunteerProvider.pastOpportunities, volunteerProvider.status),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOpportunityListWithProvider(List<VolunteerOpportunity> opportunities, VolunteerStatus status) {
    if (status == VolunteerStatus.loading) {
      return _buildLoadingState();
    } else if (status == VolunteerStatus.error) {
      return _buildErrorState();
    } else if (opportunities.isEmpty) {
      return _buildEmptyState('لا توجد فرص متاحة حالياً', Icons.search_off);
    } else {
      return ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: opportunities.length,
        itemBuilder: (context, index) {
          return _buildOpportunityCard(opportunities[index]);
        },
      );
    }
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 150,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 24,
                        width: 200,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 16,
                        width: 150,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        height: 16,
                        width: double.infinity,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 16,
                        width: double.infinity,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        height: 40,
                        width: double.infinity,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ أثناء تحميل البيانات',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshData,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4F908E),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'إعادة المحاولة',
              style: GoogleFonts.ibmPlexSansArabic(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // إعادة تعريف دالة بناء بطاقة الفرصة التطوعية لتعمل مع نموذج البيانات
  Widget _buildOpportunityCard(VolunteerOpportunity opportunity) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => VolunteerDetailsPage(opportunity: opportunity.toJson()),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    _buildNetworkImage(opportunity.imageUrl),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(opportunity.status),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getStatusIcon(opportunity.status),
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              opportunity.status,
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        opportunity.title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.business,
                            size: 18,
                            color: Color(0xFF4F908E),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            opportunity.organization,
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF4F908E),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Text(
                        opportunity.description,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 14,
                          color: Colors.grey[800],
                          height: 1.6,
                          letterSpacing: 0.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on_outlined,
                            size: 18,
                            color: const Color(0xFF4F908E).withOpacity(0.8),
                          ),
                          const SizedBox(width: 5),
                          Text(
                            opportunity.location,
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 13,
                              color: Colors.grey[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedInfoChip(
                              Icons.calendar_today_rounded,
                              opportunity.date,
                              const Color(0xFF4F908E).withOpacity(0.9),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildEnhancedInfoChip(
                              Icons.access_time_rounded,
                              opportunity.time,
                              const Color(0xFF4F908E).withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      _buildActionButtonForModel(opportunity),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              label,
              style: GoogleFonts.ibmPlexSansArabic(
                color: color.withOpacity(0.9),
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }


  void _showNotificationDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إشعارك عند فتح باب التسجيل',
          style: GoogleFonts.ibmPlexSansArabic(),
        ),
        backgroundColor: const Color(0xFFFFA000),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // دالة بناء زر العمل للنموذج
  Widget _buildActionButtonForModel(VolunteerOpportunity opportunity) {
    final bool isPast = opportunity.status == 'منتهي';
    final bool isUpcoming = opportunity.status == 'قريباً';

    if (isPast) {
      return OutlinedButton(
        onPressed: () {},
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          side: BorderSide(color: Colors.grey[400]!),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          minimumSize: const Size(double.infinity, 45),
        ),
        child: Text('عرض التفاصيل',
          style: GoogleFonts.ibmPlexSansArabic(fontWeight: FontWeight.bold)),
      );
    }

    return Container(
      height: 45,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isUpcoming
              ? [const Color(0xFFFFA000), const Color(0xFFFF8F00)]
              : [const Color(0xFF4F908E), const Color(0xFF3A7B79)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (isUpcoming ? const Color(0xFFFFA000) : const Color(0xFF4F908E))
                .withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () => isUpcoming
            ? _showNotificationDialog()
            : _launchURL(opportunity.registerUrl),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              isUpcoming ? 'تذكيري عند الإتاحة' : 'تقدم الآن',
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              isUpcoming ? Icons.notifications_active : Icons.arrow_forward,
              color: Colors.white,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  void _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      await launchUrl(uri);
    } catch (e) {
      // استخدام logger بدلاً من print في الإنتاج
      debugPrint('Could not launch $url: $e');
    }
  }
}
