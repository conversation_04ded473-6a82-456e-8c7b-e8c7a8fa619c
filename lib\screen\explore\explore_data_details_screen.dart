import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/screen/video_player/video_player_screen.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:just_audio/just_audio.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:cached_network_image/cached_network_image.dart';

class DestinationDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> destination;

  const DestinationDetailsScreen({
    super.key,
    required this.destination,
  });

  @override
  State<DestinationDetailsScreen> createState() =>
      _DestinationDetailsScreenState();
}

class _DestinationDetailsScreenState extends State<DestinationDetailsScreen>
    with TickerProviderStateMixin {
  ChewieController? _chewieController;
  VideoPlayerController? _videoPlayerController;
  AudioPlayer? _audioPlayer;
  bool _isPlaying = false;
  double _audioProgress = 0.0;
  Duration? _audioDuration;
  int _currentImageIndex = 0;
  final PageController _pageController = PageController();

  late String _currentLanguage;
  late AppLocalizations translations;

  bool _showPanoramaView = false;
  late WebViewController _webViewController;
  late AnimationController _animationController;
  late AnimationController _audioAnimationController;

  @override
  void initState() {
    super.initState();

    _currentLanguage = Provider.of<LanguageProvider>(context, listen: false).currentLanguage;

    // تهيئة المؤقتات أولاً
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _audioAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.black);

    // تهيئة الوسائط بعد تهيئة المؤقتات
    _initializeMedia();
  }

  void _initializeMedia() async {
    if (widget.destination['fullData'] != null &&
        widget.destination['fullData']['sound'] != null &&
        widget.destination['fullData']['sound'][_currentLanguage] != null) {
      _audioPlayer = AudioPlayer();
      try {
        await _audioPlayer!
            .setUrl(widget.destination['fullData']['sound'][_currentLanguage]);

        // إضافة مراقبة لحالة التشغيل
        _audioPlayer!.playerStateStream.listen((state) {
          if (mounted) {
            setState(() {
              _isPlaying = state.playing;
              if (!state.playing) {
                _audioAnimationController.stop();
              } else {
                _audioAnimationController.repeat();
              }
            });
          }
        });

        // إضافة مراقبة لمدة الصوت
        _audioPlayer!.durationStream.listen((duration) {
          if (mounted) {
            setState(() {
              _audioDuration = duration;
            });
          }
        });

        // إضافة مراقبة لموضع التشغيل
        _audioPlayer!.positionStream.listen((position) {
          if (mounted && _audioDuration != null && _audioDuration!.inMilliseconds > 0) {
            setState(() {
              _audioProgress = position.inMilliseconds / _audioDuration!.inMilliseconds;
            });
          }
        });
      } catch (e) {
        debugPrint('Error loading audio: $e');
      }
    }

    if (_hasValidData('video')) {
      final videoUrl = widget.destination['fullData']['video'][_currentLanguage];
      if (!_isPanoramaVideo(videoUrl)) {
        _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
        try {
          await _videoPlayerController!.initialize();
          _chewieController = ChewieController(
            videoPlayerController: _videoPlayerController!,
            autoPlay: false,
            looping: false,
            aspectRatio: _videoPlayerController!.value.aspectRatio,
            placeholder: Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFF4F908E),
                ),
              ),
            ),
            showControls: true,
            materialProgressColors: ChewieProgressColors(
              playedColor: const Color(0xFF4F908E),
              bufferedColor: Colors.grey[300]!,
              handleColor: const Color(0xFF4F908E),
            ),
          );
          setState(() {});
        } catch (e) {
          debugPrint('Error loading video: $e');
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _audioAnimationController.dispose();
    _videoPlayerController?.dispose();
    _chewieController?.dispose();
    _audioPlayer?.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    translations = AppLocalizations(Provider.of<LanguageProvider>(context).currentLanguage);
    final fullData = widget.destination['fullData'];
    final String title = _getTitle();

    final bool hasPanorama = _hasValidData('video') &&
        fullData!['video'][_currentLanguage].toString().contains('panoraven');

    final String? panoramaUrl =
        hasPanorama ? fullData['video'][_currentLanguage] : null;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: _showPanoramaView
            ? null
            : AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                actions: [
                  // زر مشاركة
                  Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        // يمكن إضافة وظيفة المشاركة هنا
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              '${translations.translate("share_place")} $title',
                              style: GoogleFonts.notoKufiArabic(),
                              textAlign: TextAlign.center,
                            ),
                            backgroundColor: const Color(0xFF4F908E),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            margin: const EdgeInsets.all(16),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
        body: _showPanoramaView && hasPanorama
            ? _buildPanoramaView(panoramaUrl!)
            : _buildMainContent(title, fullData, hasPanorama, panoramaUrl),
      ),
    );
  }

  Widget _buildPanoramaView(String url) {
    _webViewController.loadRequest(Uri.parse(url));

    return Stack(
      children: [
        WebViewWidget(controller: _webViewController),
        Positioned(
          top: 40,
          left: 16,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _showPanoramaView = false;
                });
              },
              borderRadius: BorderRadius.circular(30),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 30,
          left: 20,
          right: 20,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.touch_app, color: Colors.white, size: 20),
                  const SizedBox(width: 10),
                  Text(
                    translations.translate("move_screen_360"),
                    style: GoogleFonts.notoKufiArabic(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ).animate().fadeIn(delay: const Duration(milliseconds: 500)),
      ],
    );
  }

  Widget _buildMainContent(
      String title, dynamic fullData, bool hasPanorama, String? panoramaUrl) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildImageCarousel(),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                // قسم الميزات
                if (hasPanorama || _hasValidData('sound') || _hasValidData('tour_guide'))
                  Container(
                    margin: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF4F908E).withOpacity(0.1),
                          Colors.white,
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF4F908E).withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.explore_outlined,
                                  color: Color(0xFF4F908E),
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                translations.translate("explore_place"),
                                style: GoogleFonts.notoKufiArabic(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF4F908E),
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // الميزات
                        _buildFeaturesList(hasPanorama, fullData),
                      ],
                    ),
                  ).animate().fadeIn(duration: const Duration(milliseconds: 500)),

                const SizedBox(height: 24),
                _buildContentSection(title, fullData),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList(bool hasPanorama, dynamic fullData) {
    return Column(
      children: [
        if (hasPanorama)
          _buildFeatureButton(
            icon: Icons.view_in_ar,
            title: translations.translate('virtual_tour_360'),
            subtitle: translations.translate('explore_interactive'),
            onTap: () => setState(() => _showPanoramaView = true),
            gradient: const LinearGradient(
              colors: [Color(0xFF4F908E), Color(0xFF45817F)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 100)).slideX(),

        if (_hasValidData('sound'))
          _buildFeatureButton(
            icon: _isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
            title: translations.translate('audio_guide'),
            subtitle: translations.translate('listen_details'),
            onTap: _toggleAudio,
            showProgress: _isPlaying,
            progress: _audioProgress,
            gradient: const LinearGradient(
      colors: [Color(0xFF4F908E), Color(0xFF45817F)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)).slideX(),

        if (_hasValidData('tour_guide'))
          _buildFeatureButton(
            icon: Icons.ondemand_video,
            title: translations.translate('guided_tour'),
            subtitle: translations.translate('watch_video_tour'),
            onTap: () => _showMediaDialog(context),
            gradient: const LinearGradient(
            colors: [Color(0xFF4F908E), Color(0xFF45817F)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 300)).slideX(),
      ],
    );
  }

  Widget _buildImageCarousel() {
    final List<String> images = [];

    if (widget.destination['image'] != null) {
      images.add(widget.destination['image']);
    }

    if (widget.destination['fullData'] != null &&
        widget.destination['fullData']['images'] != null) {
      final List<dynamic> additionalImages =
          widget.destination['fullData']['images'];
      for (final image in additionalImages) {
        if (!images.contains(image)) {
          images.add(image);
        }
      }
    }

    if (images.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: Icon(Icons.image_not_supported, size: 64, color: Colors.grey),
        ),
      );
    }

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.45,
      child: Stack(
        children: [
          // Main Image PageView
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) => setState(() => _currentImageIndex = index),
            itemCount: images.length,
            itemBuilder: (context, index) {
              return GestureDetector(
          onTap: () => _openFullScreenGallery(images, index),
          child: Hero(
            tag: 'destination_image_$index',
            child: CachedNetworkImage(
              imageUrl: images[index],
              fit: BoxFit.cover,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(color: Color(0xFF4F908E)),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: const Icon(Icons.error_outline),
              ),
            ),
          ),
              );
            },
          ),

          // Gradient Overlay
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
          stops: const [0.5, 1.0],
              ),
            ),
          ),

          // Page Indicators
          if (images.length > 1)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              images.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                height: 8,
                width: _currentImageIndex == index ? 24 : 8,
                decoration: BoxDecoration(
            color: _currentImageIndex == index
                ? const Color(0xFF4F908E)
                : Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
              ),
            ),

          // Navigation buttons
          if (images.length > 1) ...[
            // Right button (goes to previous image in RTL)
            _buildNavigationButton(
              alignment: Alignment.centerRight,
              onTap: () {
          if (_currentImageIndex > 0) {
            _pageController.previousPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
              },
              icon: Icons.arrow_back_ios_new,
            ),
            // Left button (goes to next image in RTL)
            _buildNavigationButton(
              alignment: Alignment.centerLeft,
              onTap: () {
          if (_currentImageIndex < images.length - 1) {
            _pageController.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
              },
              icon: Icons.arrow_forward_ios,
            ),
          ],  
            Positioned(
              top: MediaQuery.of(context).size.height * 0.05,
              right: 20,
              left: 20,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.4),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  // Use constraints to make container fit the text more closely
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.85,
                  ),
                  child: Text(
                    _getTitle(),
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ).animate().fadeIn(
            delay: const Duration(milliseconds: 300),
            duration: const Duration(milliseconds: 500),
          ).slideY(begin: 0.3),
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required AlignmentGeometry alignment,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return Align(
      alignment: alignment,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(25),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Gradient gradient,
    bool showProgress = false,
    double progress = 0.0,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.notoKufiArabic(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      if (showProgress) ...[
                        const SizedBox(height: 8),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.white.withOpacity(0.3),
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            minHeight: 4,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.8),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentSection(String title, dynamic fullData) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 24),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الوصف المختصر
          if (_hasValidData('short_description'))
            Container(
              margin: const EdgeInsets.only(bottom: 24),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFF4F908E).withOpacity(0.15),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.02),
                    blurRadius: 5,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: const Color(0xFF4F908E).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF4F908E).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.info_outline,
                            color: Color(0xFF4F908E),
                            size: 22,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          translations.translate("brief"),
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4F908E),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    fullData!['short_description'][_currentLanguage],
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: 17,
                      height: 1.8,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                ],
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 400), duration: const Duration(milliseconds: 600)),

          // الوصف الكامل
          if (_hasValidData('description'))
            Container(
              margin: const EdgeInsets.only(top: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: const Color(0xFF4F908E).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF4F908E).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.article_outlined,
                            color: Color(0xFF4F908E),
                            size: 22,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          translations.translate("details"),
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4F908E),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.02),
                          blurRadius: 5,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      fullData!['description'][_currentLanguage],
                      style: GoogleFonts.notoKufiArabic(
                        fontSize: 17.0,
                        height: 1.8,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.justify,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 600), duration: const Duration(milliseconds: 600)),
        ],
      ),
    ).animate().fadeIn(duration: const Duration(milliseconds: 800));
  }

  void _openFullScreenGallery(List<String> images, int initialIndex) {
    // Creamos un nuevo controlador de página para la galería a pantalla completa
    final PageController galleryPageController = PageController(initialPage: initialIndex);

    // Variable para rastrear el índice actual en la galería
    int currentIndex = initialIndex;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => Scaffold(
            backgroundColor: Colors.black,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.close, color: Colors.white),
                ),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.share, color: Colors.white),
                  ),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'تمت مشاركة الصورة',
                          style: GoogleFonts.notoKufiArabic(),
                          textAlign: TextAlign.center,
                        ),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
              ],
            ),
            body: Stack(
              children: [
                // Galería de fotos
                PhotoViewGallery.builder(
                  scrollPhysics: const BouncingScrollPhysics(),
                  builder: (BuildContext context, int index) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: NetworkImage(images[index]),
                      initialScale: PhotoViewComputedScale.contained,
                      minScale: PhotoViewComputedScale.contained * 0.8,
                      maxScale: PhotoViewComputedScale.covered * 3.0,
                      heroAttributes: PhotoViewHeroAttributes(tag: 'destination_image_$index'),
                    );
                  },
                  itemCount: images.length,
                  loadingBuilder: (context, event) => Center(
                    child: SizedBox(
                      width: 50,
                      height: 50,
                      child: CircularProgressIndicator(
                        value: event == null
                            ? 0
                            : event.cumulativeBytesLoaded / (event.expectedTotalBytes ?? 1),
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                  ),
                  backgroundDecoration: const BoxDecoration(
                    color: Colors.black,
                  ),
                  pageController: galleryPageController,
                  onPageChanged: (index) {
                    setState(() {
                      currentIndex = index;
                    });
                  },
                ),

                // Controles de navegación e indicador de posición
                if (images.length > 1)
                  Positioned(
                    bottom: 30,
                    left: 0,
                    right: 0,
                    child: Column(
                      children: [
                        // Indicador de posición
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.6),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${currentIndex + 1} / ${images.length}',
                            style: GoogleFonts.notoKufiArabic(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Botones de navegación
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Botón anterior
                            GestureDetector(
                              onTap: currentIndex > 0
                                  ? () {
                                      galleryPageController.previousPage(
                                        duration: const Duration(milliseconds: 300),
                                        curve: Curves.easeInOut,
                                      );
                                    }
                                  : null,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: currentIndex > 0
                                      ? const Color(0xFF4F908E).withOpacity(0.8)
                                      : Colors.grey.withOpacity(0.4),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),

                            // Botón siguiente
                            GestureDetector(
                              onTap: currentIndex < images.length - 1
                                  ? () {
                                      galleryPageController.nextPage(
                                        duration: const Duration(milliseconds: 300),
                                        curve: Curves.easeInOut,
                                      );
                                    }
                                  : null,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: currentIndex < images.length - 1
                                      ? const Color(0xFF4F908E).withOpacity(0.8)
                                      : Colors.grey.withOpacity(0.4),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.arrow_forward_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _toggleAudio() async {
    if (_audioPlayer == null) return;

    final bool willPlay = !_isPlaying;

    if (_isPlaying) {
      await _audioPlayer!.pause();
      _audioAnimationController.stop();
    } else {
      await _audioPlayer!.play();
      _audioAnimationController.repeat();
    }

    if (!mounted) return;

    setState(() {
      _isPlaying = willPlay;
    });

    // عرض رسالة للمستخدم
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(
    //     content: Row(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         Icon(
    //           willPlay ? Icons.volume_up : Icons.volume_off,
    //           color: Colors.white,
    //           size: 20,
    //         ),
    //         const SizedBox(width: 8),
    //         Text(
    //           willPlay ? translations.translate('playing_audio') : translations.translate('stopped_audio'),
    //           style: GoogleFonts.notoKufiArabic(),
    //           textAlign: TextAlign.center,
    //         ),
    //       ],
    //     ),
    //     backgroundColor: const Color(0xFF4F908E),
    //     behavior: SnackBarBehavior.floating,
    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(10),
    //     ),
    //     margin: const EdgeInsets.all(16),
    //     duration: const Duration(seconds: 2),
    //   ),
    // );
  }

  bool _isPanoramaVideo(String url) {
    return url.contains('panoraven');
  }

  void _showMediaDialog(BuildContext context) {
    final fullData = widget.destination['fullData'];
    final String title = _getTitle();

    if (_hasValidData('tour_guide')) {
      final tourGuideUrl = fullData!['tour_guide'][_currentLanguage];

      if (!_isPanoramaVideo(tourGuideUrl)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideoPlayerScreen(
              videoUrl: tourGuideUrl,
              title: title,
            ),
          ),
        );
      } else {
        setState(() {
          _showPanoramaView = true;
          _webViewController.loadRequest(Uri.parse(tourGuideUrl));
        });
      }
      return;
    }

    if (_hasValidData('video')) {
      final videoUrl = fullData!['video'][_currentLanguage];
      if (!_isPanoramaVideo(videoUrl)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideoPlayerScreen(
              videoUrl: videoUrl,
              title: title,
            ),
          ),
        );
      } else {
        setState(() {
          _showPanoramaView = true;
          _webViewController.loadRequest(Uri.parse(videoUrl));
        });
      }
    }
  }

  String _getTitle() {
    if (widget.destination['fullData'] != null && 
        widget.destination['fullData']['title'] != null &&
        widget.destination['fullData']['title'][_currentLanguage] != null) {
      return widget.destination['fullData']['title'][_currentLanguage];
    } else if (widget.destination['title'] != null) {
      if (widget.destination['title'] is Map) {
        final titleMap = widget.destination['title'] as Map;
        return titleMap[_currentLanguage]?.toString() ?? translations.translate('place_details');
      } else {
        return widget.destination['title'].toString();
      }
    }
    return translations.translate('place_details');
  }

  bool _hasValidData(String dataType) {
    final fullData = widget.destination['fullData'];
    return fullData != null &&
        fullData[dataType] != null &&
        fullData[dataType] is Map &&
        fullData[dataType][_currentLanguage] != null;
  }
}
