import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/mosque_data_model.dart';

class MosqueService {
  // عنوان API الخاص بالخادم (يمكن تغييره لاحقًا)
  static const String apiUrl = 'https://api.wifada.com/mosques';
  
  // جلب بيانات المسجد النبوي من الخادم
  Future<MosqueDataModel> getMosqueData(String mosqueId) async {
    try {
      // في بيئة الإنتاج، سنستخدم هذا الكود للاتصال بالخادم الفعلي
      // ignore: dead_code
      if (false) { // تغيير إلى true عند توفر الخادم
      } 
      // في بيئة التطوير، نستخدم ملف JSON محلي
      else {
        // محاكاة تأخير الشبكة
        await Future.delayed(const Duration(milliseconds: 800));
        
        // قراءة الملف المحلي
        final String jsonData = await rootBundle.loadString('assets/data/mosque_data.json');
        return MosqueDataModel.fromJson(json.decode(jsonData));
      }
    } catch (e) {
      throw Exception('حدث خطأ أثناء جلب بيانات المسجد: $e');
    }
  }
  
  // جلب بيانات البث المباشر للمسجد
  Future<LiveStreamData> getLiveStreamData(String mosqueId) async {
    try {
      final mosqueData = await getMosqueData(mosqueId);
      return mosqueData.liveStream;
    } catch (e) {
      throw Exception('حدث خطأ أثناء جلب بيانات البث المباشر: $e');
    }
  }
  
  // جلب بيانات الإرشادات للمسجد
  Future<List<GuidelineData>> getGuidelinesData(String mosqueId) async {
    try {
      final mosqueData = await getMosqueData(mosqueId);
      return mosqueData.guidelines;
    } catch (e) {
      throw Exception('حدث خطأ أثناء جلب بيانات الإرشادات: $e');
    }
  }
  
  // جلب بيانات خريطة المسجد
  Future<MapData> getMapData(String mosqueId) async {
    try {
      final mosqueData = await getMosqueData(mosqueId);
      return mosqueData.mapData;
    } catch (e) {
      throw Exception('حدث خطأ أثناء جلب بيانات الخريطة: $e');
    }
  }
  
  // جلب بيانات مرافق المسجد
  Future<List<FacilityData>> getFacilitiesData(String mosqueId) async {
    try {
      final mosqueData = await getMosqueData(mosqueId);
      return mosqueData.facilities;
    } catch (e) {
      throw Exception('حدث خطأ أثناء جلب بيانات المرافق: $e');
    }
  }
}
