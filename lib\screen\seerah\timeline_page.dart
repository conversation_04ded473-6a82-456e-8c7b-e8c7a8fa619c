import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';

class TimelinePage extends StatefulWidget {
  const TimelinePage({super.key});

  @override
  State<TimelinePage> createState() => _TimelinePageState();
}

class _TimelinePageState extends State<TimelinePage> {
  int selectedEventIndex = 0;

  final List<Map<String, dynamic>> timelineEvents = [
    {
      'year': '571 م',
      'age': 'الولادة',
      'title': 'ولادة النبي محمد ﷺ',
      'description': 'وُلد خير البشر في مكة المكرمة في عام الفيل',
      'details':
          'وُلد النبي محمد ﷺ في شهر ربيع الأول من عام الفيل الموافق 571 ميلادية. وُلد يتيم الأب، حيث توفي والده عبد الله قبل ولادته. كفله جده عبد المطلب، وبعد وفاة جده كفله عمه أبو طالب.',
      'significance': 'بداية حياة خاتم الأنبياء والمرسلين',
      'icon': Iconsax.heart,
      'color': const Color(0xFF3498DB),
      'category': 'الحياة المبكرة',
    },
    {
      'year': '576 م',
      'age': '6 سنوات',
      'title': 'وفاة أمه آمنة بنت وهب',
      'description': 'أصبح النبي ﷺ يتيم الأبوين',
      'details':
          'توفيت أمه آمنة بنت وهب وهو في السادسة من عمره في الأبواء وهي عائدة من زيارة أهلها في المدينة المنورة. فأصبح يتيم الأبوين، وتولى رعايته جده عبد المطلب.',
      'significance': 'تجربة اليتم التي أثرت في شخصيته الكريمة',
      'icon': Iconsax.user,
      'color': const Color(0xFF95A5A6),
      'category': 'الحياة المبكرة',
    },
    {
      'year': '595 م',
      'age': '25 سنة',
      'title': 'زواجه من خديجة رضي الله عنها',
      'description': 'الزواج المبارك من أم المؤمنين خديجة',
      'details':
          'تزوج النبي ﷺ من خديجة بنت خويلد رضي الله عنها وكان عمره 25 سنة وعمرها 40 سنة. كانت تاجرة ثرية وذات شرف ومكانة في قريش. عاشا معاً 25 سنة في سعادة ووئام.',
      'significance': 'بداية الاستقرار العائلي والدعم المعنوي',
      'icon': Iconsax.heart,
      'color': const Color(0xFFE91E63),
      'category': 'الحياة الشخصية',
    },
    {
      'year': '610 م',
      'age': '40 سنة',
      'title': 'بداية الوحي في غار حراء',
      'description': 'نزول أول آيات القرآن الكريم',
      'details':
          'في شهر رمضان المبارك، نزل الوحي على النبي ﷺ في غار حراء بآيات "اقرأ باسم ربك الذي خلق". كان يتعبد في الغار ويتفكر في خلق الله. هذا الحدث غير مجرى التاريخ.',
      'significance': 'بداية الرسالة الخاتمة للبشرية',
      'icon': Iconsax.book_1,
      'color': const Color(0xFF9B59B6),
      'category': 'الرسالة',
    },
    {
      'year': '613 م',
      'age': '43 سنة',
      'title': 'الدعوة الجهرية',
      'description': 'بداية الدعوة العلنية للإسلام',
      'details':
          'بعد ثلاث سنوات من الدعوة السرية، أمر الله نبيه بالجهر بالدعوة. صعد النبي ﷺ على الصفا ونادى قريش وأعلن دعوته. واجه معارضة شديدة وأذى كبير من قومه.',
      'significance': 'انطلاق الدعوة الإسلامية علناً',
      'icon': Iconsax.microphone,
      'color': const Color(0xFFE74C3C),
      'category': 'الرسالة',
    },
    {
      'year': '622 م',
      'age': '53 سنة',
      'title': 'الهجرة النبوية المباركة',
      'description': 'الانتقال من مكة إلى المدينة المنورة',
      'details':
          'هاجر النبي ﷺ مع صاحبه أبي بكر الصديق من مكة إلى المدينة المنورة هرباً من أذى قريش. استغرقت الرحلة عدة أيام مروا خلالها بغار ثور. هذا الحدث بداية التاريخ الهجري.',
      'significance': 'بداية الدولة الإسلامية الأولى',
      'icon': Iconsax.location,
      'color': const Color(0xFF27AE60),
      'category': 'الهجرة والدولة',
    },
    {
      'year': '629 م',
      'age': '59 سنة',
      'title': 'صلح الحديبية',
      'description': 'معاهدة السلام التاريخية مع قريش',
      'details':
          'وقع النبي ﷺ صلح الحديبية مع قريش عندما منعوه من دخول مكة للعمرة. رغم أن الصحابة رأوه هدنة، إلا أن الله سماه فتحاً مبيناً. فتح هذا الصلح الطريق لانتشار الإسلام.',
      'significance': 'دبلوماسية نبوية حكيمة غيرت مجرى الأحداث',
      'icon': Iconsax.people,
      'color': const Color(0xFFF39C12),
      'category': 'الفتوحات',
    },
    {
      'year': '630 م',
      'age': '60 سنة',
      'title': 'فتح مكة المكرمة',
      'description': 'الفتح المبين والعفو العام',
      'details':
          'فتح النبي ﷺ مكة المكرمة فتحاً مبيناً بعد أن نقضت قريش صلح الحديبية. دخل مكة في عشرة آلاف مقاتل، وأعلن العفو العام قائلاً: "اذهبوا فأنتم الطلقاء". طهر الكعبة من الأصنام.',
      'significance': 'انتصار الإسلام وتطهير البيت الحرام',
      'icon': Iconsax.flag,
      'color': const Color(0xFF8E44AD),
      'category': 'الفتوحات',
    },
    {
      'year': '632 م',
      'age': '63 سنة',
      'title': 'حجة الوداع والوفاة',
      'description': 'آخر حج وانتقال النبي ﷺ إلى الرفيق الأعلى',
      'details':
          'أدى النبي ﷺ حجة الوداع وألقى خطبته الشهيرة أمام أكثر من مئة ألف مسلم. أكمل الله الدين وأتم النعمة. توفي النبي ﷺ في المدينة المنورة في شهر ربيع الأول.',
      'significance': 'ختام الرسالة وانتقال القيادة للأمة',
      'icon': Iconsax.home_2,
      'color': const Color(0xFF34495E),
      'category': 'الختام',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          'الخط الزمني التفاعلي',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          _buildTimelineHeader(),
          Expanded(
            child: Row(
              children: [
                _buildTimelineList(),
                _buildEventDetails(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF4F908E),
            const Color(0xFF4F908E).withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Iconsax.calendar,
            size: 48,
            color: Colors.white,
          ).animate().scale(duration: const Duration(milliseconds: 600)),
          const SizedBox(height: 12),
          Text(
            'رحلة حياة النبي محمد ﷺ',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
          const SizedBox(height: 8),
          Text(
            'اكتشف الأحداث المهمة في حياة خير البشر',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
        ],
      ),
    );
  }

  Widget _buildTimelineList() {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      width: screenWidth < 600 ? screenWidth * 0.35 : screenWidth * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFE0E0E0), width: 1),
        ),
      ),
      child: ListView.builder(
        padding: EdgeInsets.all(screenWidth < 600 ? 12 : 16),
        itemCount: timelineEvents.length,
        itemBuilder: (context, index) {
          return _buildTimelineItem(index);
        },
      ),
    );
  }

  Widget _buildTimelineItem(int index) {
    final event = timelineEvents[index];
    final isSelected = index == selectedEventIndex;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedEventIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            // Timeline line and dot
            Column(
              children: [
                if (index > 0)
                  Container(
                    width: 2,
                    height: 20,
                    color: const Color(0xFF4F908E).withValues(alpha: 0.3),
                  ),
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color:
                        isSelected ? const Color(0xFF4F908E) : Colors.grey[300],
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF4F908E)
                          : Colors.grey[400]!,
                      width: 2,
                    ),
                  ),
                ),
                if (index < timelineEvents.length - 1)
                  Container(
                    width: 2,
                    height: 40,
                    color: const Color(0xFF4F908E).withValues(alpha: 0.3),
                  ),
              ],
            ),
            const SizedBox(width: 16),
            // Event info
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF4F908E).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF4F908E)
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          event['icon'],
                          size: 14,
                          color: event['color'],
                        ),
                        const SizedBox(width: 6),
                        Flexible(
                          child: Text(
                            event['year'],
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF4F908E),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: (event['color'] as Color).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              (event['color'] as Color).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        event['age'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 9,
                          color: event['color'],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      event['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      event['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideX(begin: -0.3, end: 0);
  }

  Widget _buildEventDetails() {
    final event = timelineEvents[selectedEventIndex];

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Event header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: (event['color'] as Color).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      event['icon'],
                      size: 32,
                      color: event['color'],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event['year'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4F908E),
                          ),
                        ),
                        Text(
                          event['title'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C3E50),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Event image
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      (event['color'] as Color).withValues(alpha: 0.1),
                      (event['color'] as Color).withValues(alpha: 0.3),
                    ],
                  ),
                  border: Border.all(
                    color: (event['color'] as Color).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        event['icon'],
                        size: 60,
                        color: event['color'],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        event['title'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: event['color'],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // تفاصيل الحدث
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: (event['color'] as Color).withValues(alpha: 0.2),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Iconsax.document_text,
                          color: event['color'],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'تفاصيل الحدث',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C3E50),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      event['details'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 15,
                        color: Colors.grey[700],
                        height: 1.7,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // الأهمية التاريخية
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      (event['color'] as Color).withValues(alpha: 0.1),
                      (event['color'] as Color).withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: (event['color'] as Color).withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Iconsax.star,
                          color: event['color'],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الأهمية التاريخية',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: event['color'],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      event['significance'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        color: Colors.grey[700],
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // فئة الحدث
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: (event['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: (event['color'] as Color).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Iconsax.category,
                      color: event['color'],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      event['category'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: event['color'],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
