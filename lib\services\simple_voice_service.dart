import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:just_audio/just_audio.dart';

class VoiceWebSocketService {
  static const String _wsUrl =
      'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01';
  static WebSocketChannel? _channel;
  static bool _isConnected = false;
  static Function(String)? onTextResponse;
  static Function(String)? onAudioResponse;
  static Function(String)? onError;
  static Function()? onAudioPlaybackComplete;

  // مشغل الصوت
  static AudioPlayer? _audioPlayer;
  static final List<int> _audioBuffer = [];

  // الاتصال بـ WebSocket للمحادثة الصوتية المباشرة
  static Future<WebSocketChannel?> connect() async {
    try {
      log("🎤 Connecting to OpenAI Realtime API...");

      // التحقق من وجود مفتاح API
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        log("❌ OpenAI API key not found");
        return null;
      }

      _channel = IOWebSocketChannel.connect(
        _wsUrl,
        headers: {
          'Authorization': 'Bearer $apiKey',
          'OpenAI-Beta': 'realtime=v1',
        },
      );

      // إرسال إعدادات الجلسة
      final sessionConfig = {
        'type': 'session.update',
        'session': {
          'modalities': ['text', 'audio'],
          'instructions':
              """أنت وفادة، مساعدة صوتية ودليل سياحي للمدينة المنورة.

🎤 في المحادثة الصوتية:
- ابدئي بالسلام والترحيب في أول مرة فقط
- كوني مفيدة وودودة
- اجعلي إجاباتك مناسبة للاستماع (ليس طويلة جداً)

📍 تخصصك:
- تاريخ المدينة المنورة
- قصص الصحابة والسيرة النبوية
- فضائل المدينة وبركاتها
- المعالم المقدسة والمساجد
- نصائح عملية للزوار

🎯 أسلوبك الصوتي:
- كلام واضح ومفهوم
- توقفات مناسبة
- نبرة ودودة ومرحبة""",
          'voice': 'alloy',
          'input_audio_format': 'pcm16',
          'output_audio_format': 'pcm16',
          'input_audio_transcription': {'model': 'whisper-1'},
          'turn_detection': {
            'type': 'server_vad',
            'threshold': 0.5,
            'prefix_padding_ms': 300,
            'silence_duration_ms': 500
          },
          'tools': [],
          'tool_choice': 'auto',
          'temperature': 0.8,
          'max_response_output_tokens': 4096
        }
      };

      _channel!.sink.add(jsonEncode(sessionConfig));
      _isConnected = true;
      log("✅ Voice chat session configured");

      // إعداد معالج الردود
      _setupResponseHandler();

      return _channel;
    } catch (e) {
      log("❌ Voice chat connection error: $e");
      _isConnected = false;
      return null;
    }
  }

  // إعداد معالج الردود
  static void _setupResponseHandler() {
    if (_channel != null) {
      _channel!.stream.listen(
        (data) {
          try {
            final response = jsonDecode(data);
            _handleResponse(response);
          } catch (e) {
            log("❌ Error parsing response: $e");
            onError?.call("خطأ في معالجة الرد: $e");
          }
        },
        onError: (error) {
          log("❌ WebSocket stream error: $error");
          onError?.call("خطأ في الاتصال: $error");
          _isConnected = false;
        },
        onDone: () {
          log("🔴 WebSocket connection closed");
          _isConnected = false;
        },
      );
    }
  }

  // معالجة الردود من OpenAI
  static void _handleResponse(Map<String, dynamic> response) {
    final type = response['type'];

    switch (type) {
      case 'session.created':
        log("✅ Session created successfully");
        break;
      case 'session.updated':
        log("✅ Session updated successfully");
        break;
      case 'response.text.delta':
        final delta = response['delta'] ?? '';
        onTextResponse?.call(delta);
        break;
      case 'response.text.done':
        log("✅ Text response completed");
        break;
      case 'response.audio.delta':
        final audio = response['delta'] ?? '';
        if (audio.isNotEmpty) {
          _handleAudioDelta(audio);
        }
        onAudioResponse?.call(audio);
        break;
      case 'response.audio.done':
        log("✅ Audio response completed");
        _playAudioBuffer();
        break;
      case 'error':
        final errorMsg = response['error']?['message'] ?? 'خطأ غير معروف';
        log("❌ OpenAI error: $errorMsg");
        onError?.call(errorMsg);
        break;
      case 'input_audio_buffer.speech_started':
        log("🎤 Speech started");
        break;
      case 'input_audio_buffer.speech_stopped':
        log("🎤 Speech stopped");
        break;
      default:
        log("📝 Received: $type");
    }
  }

  // إرسال رسالة نصية عبر WebSocket
  static void sendTextMessage(String message) {
    if (_channel != null && _isConnected) {
      try {
        log("📤 Sending message: $message");

        final messageData = {
          'type': 'conversation.item.create',
          'item': {
            'type': 'message',
            'role': 'user',
            'content': [
              {'type': 'input_text', 'text': message}
            ]
          }
        };

        _channel!.sink.add(jsonEncode(messageData));

        // طلب الرد
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio'],
            'instructions':
                'أجيبي على سؤال المستخدم بشكل مفيد ومناسب للمحادثة الصوتية. اجعلي الرد واضح ومختصر.'
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("✅ Text message sent successfully");
      } catch (e) {
        log("❌ Error sending text message: $e");
        onError?.call("خطأ في إرسال الرسالة: $e");
      }
    } else {
      log("❌ WebSocket not connected");
      onError?.call("الاتصال غير متوفر. حاول الاتصال أولاً.");
    }
  }

  // إرسال بيانات صوتية
  static void sendAudioData(List<int> audioData) {
    if (_channel != null && _isConnected) {
      try {
        final audioMessage = {
          'type': 'input_audio_buffer.append',
          'audio': base64Encode(audioData)
        };

        _channel!.sink.add(jsonEncode(audioMessage));
        log("🎤 Audio data sent");
      } catch (e) {
        log("❌ Error sending audio data: $e");
      }
    }
  }

  // إنهاء إدخال الصوت وطلب الرد
  static void commitAudio() {
    if (_channel != null && _isConnected) {
      try {
        final commitMessage = {'type': 'input_audio_buffer.commit'};

        _channel!.sink.add(jsonEncode(commitMessage));

        // طلب الرد
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio']
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("🎤 Audio committed and response requested");
      } catch (e) {
        log("❌ Error committing audio: $e");
      }
    }
  }

  // معالجة بيانات الصوت الواردة
  static void _handleAudioDelta(String audioBase64) {
    try {
      final audioBytes = base64Decode(audioBase64);
      _audioBuffer.addAll(audioBytes);
      log("🔊 Audio delta received: ${audioBytes.length} bytes");
    } catch (e) {
      log("❌ Error handling audio delta: $e");
    }
  }

  // تشغيل الصوت المجمع
  static Future<void> _playAudioBuffer() async {
    if (_audioBuffer.isEmpty) {
      log("⚠️ No audio data to play");
      return;
    }

    try {
      log("🎵 Playing audio buffer: ${_audioBuffer.length} bytes");

      // إنشاء مشغل صوت جديد إذا لم يكن موجود
      _audioPlayer ??= AudioPlayer();

      // تحويل PCM16 إلى WAV
      final wavData = _createWavFromPcm(_audioBuffer);

      // تشغيل الصوت من الذاكرة
      await _audioPlayer!.setAudioSource(
        AudioSource.uri(
          Uri.dataFromBytes(
            wavData,
            mimeType: 'audio/wav',
          ),
        ),
      );

      await _audioPlayer!.play();
      log("✅ Audio playback started");

      // الاستماع لانتهاء التشغيل
      _audioPlayer!.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          log("✅ Audio playback completed");
          onAudioPlaybackComplete?.call();
        }
      });

      // تنظيف البافر
      _audioBuffer.clear();
    } catch (e) {
      log("❌ Error playing audio: $e");
      _audioBuffer.clear();
    }
  }

  // تحويل PCM16 إلى WAV
  static Uint8List _createWavFromPcm(List<int> pcmData) {
    const int sampleRate = 24000; // OpenAI Realtime API sample rate
    const int channels = 1; // Mono
    const int bitsPerSample = 16;

    final int dataSize = pcmData.length;
    final int fileSize = 36 + dataSize;

    final List<int> wavHeader = [
      // RIFF header
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      fileSize & 0xff, (fileSize >> 8) & 0xff, (fileSize >> 16) & 0xff,
      (fileSize >> 24) & 0xff,
      0x57, 0x41, 0x56, 0x45, // "WAVE"

      // fmt chunk
      0x66, 0x6d, 0x74, 0x20, // "fmt "
      16, 0, 0, 0, // chunk size
      1, 0, // audio format (PCM)
      channels, 0, // number of channels
      sampleRate & 0xff, (sampleRate >> 8) & 0xff, (sampleRate >> 16) & 0xff,
      (sampleRate >> 24) & 0xff,
      (sampleRate * channels * bitsPerSample ~/ 8) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 8) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 16) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 24) & 0xff,
      (channels * bitsPerSample ~/ 8), 0, // block align
      bitsPerSample, 0, // bits per sample

      // data chunk
      0x64, 0x61, 0x74, 0x61, // "data"
      dataSize & 0xff, (dataSize >> 8) & 0xff, (dataSize >> 16) & 0xff,
      (dataSize >> 24) & 0xff,
    ];

    return Uint8List.fromList([...wavHeader, ...pcmData]);
  }

  // إغلاق الاتصال
  static void disconnect() {
    try {
      _channel?.sink.close();
      _channel = null;
      _isConnected = false;

      // إيقاف وتنظيف مشغل الصوت
      _audioPlayer?.stop();
      _audioPlayer?.dispose();
      _audioPlayer = null;
      _audioBuffer.clear();

      log("🔴 WebSocket disconnected");
    } catch (e) {
      log("❌ Error disconnecting: $e");
    }
  }

  // التحقق من حالة الاتصال
  static bool get isConnected => _isConnected;

  // الحصول على قناة WebSocket للاستماع للردود
  static WebSocketChannel? get channel => _channel;
}
