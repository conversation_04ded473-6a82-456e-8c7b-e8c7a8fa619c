import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';
import 'package:wiffada/screen/profile/privacy_policy_bottom_sheet.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import 'language_settings_screen.dart';
import 'contact_bottom_sheet.dart';
import 'about_bottom_sheet.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({Key? key}) : super(key: key);

  Future<void> _launchAppRating() async {
    const url = 'https://play.google.com/store/apps/details?id=com.wifada.app';
    if (await canLaunch(url)) {
      await launch(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 65,
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: Text(
          translations.translate('profile'),
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/wifada_logo.png',
                  width: 32,
                  height: 32,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Container(
                color: Colors.white,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    _buildSectionTitle(translations.translate('preferences')),
                    const SizedBox(height: 10),
                    _buildOptionCard(
                      title: translations.translate('language'),
                      subtitle: _getLanguageSubtitle(languageProvider.currentLanguage),
                      icon: Iconsax.language_square,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LanguageSettingsScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 25),
                    
                    _buildSectionTitle(translations.translate('support_help')),
                    const SizedBox(height: 10),
                    _buildOptionCard(
                      title: translations.translate('contact_us'),
                      subtitle: translations.translate('help_queries'),
                      icon: Iconsax.message,
                      onTap: () => _showContactInfo(context),
                    ),
                    _buildOptionCard(
                      title: translations.translate('about_app'),
                      subtitle: '${translations.translate('version')} 1.0.0',
                      icon: Iconsax.info_circle,
                      onTap: () => _showAboutApp(context),
                    ),
                    const SizedBox(height: 25),

                    _buildSectionTitle(translations.translate('privacy_rating')),
                    const SizedBox(height: 10),
                    _buildOptionCard(
                      title: translations.translate('privacy_policy'),
                      subtitle: translations.translate('terms_of_use'),
                      icon: Iconsax.security,
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                          ),
                          builder: (context) => const PrivacyPolicyBottomSheet(),
                        );
                      },
                    ),
                    _buildOptionCard(
                      title: translations.translate('rate_app'),
                      subtitle: translations.translate('share_opinion'),
                      icon: Iconsax.star,
                      onTap: () => _launchAppRating(),
                    ),
                    const SizedBox(height: 30),
                    Center(
                      child: Text(
                        translations.translate('app_version'),
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 13,
                          color: Colors.grey[400],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getLanguageSubtitle(String languageCode) {
    final languages = {
      'ar': 'العربية',
      'en': 'English',
      // 'ur': 'اردو',
    };
    return languages[languageCode] ?? 'العربية';
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.ibmPlexSansArabic(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF4F908E),
      ),
    );
  }

  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool showToggle = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4F908E).withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4F908E).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF4F908E),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2D3142),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                if (showToggle)
                  Switch(
                    value: false,
                    onChanged: (value) {},
                    activeColor: const Color(0xFF4F908E),
                  )
                else
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[300],
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showContactInfo(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const ContactBottomSheet(),
    );
  }

  void _showAboutApp(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AboutBottomSheet(),
    );
  }
}
