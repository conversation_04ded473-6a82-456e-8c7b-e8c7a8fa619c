import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // إضافة استيراد للتحكم في اتجاه الشاشة
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';

class MapTab extends StatelessWidget {
  const MapTab({super.key});

  @override
  Widget build(BuildContext context) {
    // استخدام SingleChildScrollView لمنع مشكلة التجاوز
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خريطة المسجد النبوي',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF4F908E),
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () => _showFullScreenImage(context),
            child: <PERSON>(
              tag: 'mosque_map_fullscreen',
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: CachedNetworkImage(
                      imageUrl: 'https://hajjumrahplanner.com/wp-content/uploads/2024/06/%D8%AE%D8%B1%D9%8A%D8%B7%D8%A9-%D8%A7%D9%84%D9%85%D8%B3%D8%AC%D8%AF-%D8%A7%D9%84%D9%86%D8%A8%D9%88%D9%8A.jpg',
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Center(
            child: Text(
              'انقر على الخريطة للتكبير',
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          // إضافة مساحة في الأسفل
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showFullScreenImage(BuildContext context) {
    // الحفاظ على اتجاه الشاشة في الوضع العمودي فقط
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            title: const Text(
              'خريطة المسجد النبوي',
              style: TextStyle(color: Colors.white),
            ),
          ),
          body: Center(
            child: PhotoView(
              imageProvider: const CachedNetworkImageProvider(
                'https://hajjumrahplanner.com/wp-content/uploads/2024/06/%D8%AE%D8%B1%D9%8A%D8%B7%D8%A9-%D8%A7%D9%84%D9%85%D8%B3%D8%AC%D8%AF-%D8%A7%D9%84%D9%86%D8%A8%D9%88%D9%8A.jpg'
              ),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 5, // زيادة مستوى التكبير
              heroAttributes: const PhotoViewHeroAttributes(
                tag: 'mosque_map_fullscreen',
              ),
              backgroundDecoration: const BoxDecoration(
                color: Colors.black,
              ),
              // تعيين نسبة العرض إلى الارتفاع للصورة
              basePosition: Alignment.center,
              // إضافة حدود للصورة
              filterQuality: FilterQuality.high,
              enableRotation: false, // منع الدوران
              initialScale: PhotoViewComputedScale.contained, // البدء بمقياس يناسب الشاشة
            ),
          ),
        ),
      ),
    );
  }
}
