import 'dart:convert'; // تأكد من استيراد مكتبة التشفير
import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

class ApiService {
  // نستخدم نفس مفتاح API للمحادثة النصية والصوتية
  Future<String> sendMessageToChatGPT(String message) async {
    const url = "https://api.openai.com/v1/chat/completions";

    try {
      log("🟢 Sending request to ChatGPT...");
      log("User Message: $message");

      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer ${dotenv.env['OPENAI_API_KEY']}",
        },
        body: jsonEncode({
          "model": "gpt-3.5-turbo",
          "messages": [
            {
              "role": "system",
              "content": " وتقدم خدمات المساعدة والاشاد للزوار عن المدينة المنورة ايضا اجعل ردودك جميلة وضع رموز تعبيريةوايقتباسات انت دليل لزوار المدينة المنورة تخدم زوار المدينة المنورة والحجاج ايضا تعمل مع جمعية وفادة التطوعية كن لطيف "
            },
            {"role": "user", "content": message},
          ],
          "max_tokens": 500, // زيادة الحد الأقصى لعدد الأحرف
        }),
      );

      log("🔵 Response Status Code: ${response.statusCode}");

      if (response.statusCode == 200) {
        // فك تشفير النصوص المستلمة
        final decodedResponse = utf8.decode(response.bodyBytes);
        final data = jsonDecode(decodedResponse);

        // استخراج النص من الحقل content
        final gptResponse =
            data["choices"][0]["message"]["content"].toString().trim();

        log("🟢 Final GPT Response (content only): $gptResponse");
        return gptResponse;
      } else {
        log("🔴 Error: ${response.statusCode}");
        return "نعتذر ولكن الخدمة مغلقة حاليا";
      }
    } catch (e) {
      log("🔴 Exception: $e");
      return "حدث خطأ أثناء إرسال الرسالة: $e";
    }
  }
}
