import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  static const String _languageKey = 'language';
  Locale _locale = const Locale('ar');
  SharedPreferences? _prefs;

  LocaleProvider() {
    _loadLanguage();
  }

  Locale get locale => _locale;

  Future<void> _loadLanguage() async {
    _prefs = await SharedPreferences.getInstance();
    String languageCode = _prefs?.getString(_languageKey) ?? 'ar';
    _locale = Locale(languageCode);
    notifyListeners();
  }

  /// Sets the locale of the application.
  ///
  /// [languageCode] is the code of the language to set, e.g. 'en' or 'ar'.
  ///
  /// If the locale is changed, the [notifyListeners] method is called to
  /// notify all listeners that the locale has changed.
  Future<void> setLocale(String languageCode) async {
    if (_locale.languageCode != languageCode) {
      // Set the new locale.
      _locale = Locale(languageCode);

      // Save the new locale to the SharedPreferences.
      await _prefs?.setString(_languageKey, languageCode);

      // Notify all listeners that the locale has changed.
      notifyListeners();
    }
  }

  bool get isArabic => _locale.languageCode == 'ar';
  
  TextDirection get textDirection => isArabic ? TextDirection.rtl : TextDirection.ltr;
}
