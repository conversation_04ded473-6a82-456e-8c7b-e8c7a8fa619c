import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomSnackbar {
  static void show({
    required BuildContext context,
    required String message,
    IconData? icon,
    Color backgroundColor = const Color(0xFF307371),
    Duration duration = const Duration(seconds: 2),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 10),
            ],
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.ibmPlexSansArabic(),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        duration: duration,
        backgroundColor: backgroundColor,
      ),
    );
  }
}
