class VolunteerOpportunity {
  final String id;
  final String title;
  final String organization;
  final String description;
  final String location;
  final String date;
  final String time;
  final int seats;
  final String? imageUrl;
  final List<String>? requirements;
  final List<String>? benefits;
  final String status;
  final String registerUrl;

  VolunteerOpportunity({
    required this.id,
    required this.title,
    required this.organization,
    required this.description,
    required this.location,
    required this.date,
    required this.time,
    required this.seats,
    this.imageUrl,
    this.requirements,
    this.benefits,
    required this.status,
    required this.registerUrl,
  });

  factory VolunteerOpportunity.fromJson(Map<String, dynamic> json) {
    return VolunteerOpportunity(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      organization: json['organization'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      seats: json['seats'] ?? 0,
      imageUrl: json['imageUrl'],
      requirements: json['requirements'] != null
          ? List<String>.from(json['requirements'])
          : null,
      benefits: json['benefits'] != null
          ? List<String>.from(json['benefits'])
          : null,
      status: json['status'] ?? '',
      registerUrl: json['registerUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'organization': organization,
      'description': description,
      'location': location,
      'date': date,
      'time': time,
      'seats': seats,
      'imageUrl': imageUrl,
      'requirements': requirements,
      'benefits': benefits,
      'status': status,
      'registerUrl': registerUrl,
    };
  }
}
