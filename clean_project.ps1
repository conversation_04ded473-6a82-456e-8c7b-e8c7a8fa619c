# Project Cleanup Script

Write-Host "Cleaning project..." -ForegroundColor Green

# Clean Flutter
Write-Host "Cleaning Flutter files..." -ForegroundColor Yellow
flutter clean

# Delete build folders
Write-Host "Deleting build folders..." -ForegroundColor Yellow
$foldersToDelete = @(
    "build",
    ".dart_tool",
    "android\.gradle",
    "android\app\build",
    "ios\build",
    "windows\build",
    "linux\build",
    "macos\build",
    "web\build"
)

foreach ($folder in $foldersToDelete) {
    if (Test-Path $folder) {
        Write-Host "Deleting: $folder" -ForegroundColor Red
        Remove-Item -Path $folder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Delete temp files
Write-Host "Deleting temp files..." -ForegroundColor Yellow
$tempFiles = @(
    "*.tmp",
    "*.temp",
    "*.log",
    "*.cache",
    ".DS_Store",
    "Thumbs.db"
)

foreach ($pattern in $tempFiles) {
    Get-ChildItem -Path . -Recurse -Name $pattern -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "Deleting: $_" -ForegroundColor Red
        Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    }
}

# Clean pub cache
Write-Host "Cleaning pub cache..." -ForegroundColor Yellow
flutter pub cache clean

# Reload dependencies
Write-Host "Reloading dependencies..." -ForegroundColor Yellow
flutter pub get

Write-Host "Project cleaned successfully!" -ForegroundColor Green
Write-Host "Disk space saved significantly" -ForegroundColor Cyan
