import 'package:iconsax/iconsax.dart';
import 'package:flutter/material.dart';

class SeerahTimelineData {
  static final List<Map<String, dynamic>> timelineEvents = [
    {
      'id': 1,
      'year': '571 م',
      'hijriYear': 'عام الفيل',
      'age': 'الولادة',
      'title': 'ولادة النبي محمد ﷺ',
      'subtitle': 'ولادة خير البشر في مكة المكرمة',
      'description': 'وُلد النبي محمد ﷺ في مكة المكرمة في عام الفيل',
      'details':
          '''وُلد النبي محمد ﷺ في شهر ربيع الأول من عام الفيل الموافق 571 ميلادية في مكة المكرمة. وُلد يتيم الأب، حيث توفي والده عبد الله بن عبد المطلب قبل ولادته بأشهر قليلة. 

كان مولده في دار أبي طالب في شعب بني هاشم، وقد أرضعته أمه آمنة بنت وهب أياماً قليلة، ثم أرضعته ثويبة مولاة أبي لهب، ثم حليمة السعدية من بني سعد.

شهد عام ولادته حادثة الفيل المشهورة، حين أراد أبرهة الحبشي هدم الكعبة فأهلكه الله وجيشه، وقد ذكر الله هذه الحادثة في القرآن الكريم في سورة الفيل.''',
      'significance':
          'بداية حياة خاتم الأنبياء والمرسلين الذي سيغير مجرى التاريخ',
      'location': 'مكة المكرمة - شعب بني هاشم',
      'icon': Iconsax.heart,
      'color': const Color(0xFF3498DB),
      'category': 'الحياة المبكرة',
      'keyFigures': [
        'آمنة بنت وهب (الأم)',
        'عبد المطلب (الجد)',
        'حليمة السعدية (المرضعة)'
      ],
      'relatedEvents': ['حادثة الفيل', 'إرضاع حليمة السعدية'],
      'lessons': ['قدرة الله على حفظ أوليائه', 'بداية الرحمة المهداة للعالمين'],
    },
    {
      'id': 2,
      'year': '576 م',
      'hijriYear': '5 ق.هـ',
      'age': '6 سنوات',
      'title': 'وفاة أمه آمنة بنت وهب',
      'subtitle': 'أصبح النبي ﷺ يتيم الأبوين',
      'description': 'توفيت أمه آمنة وهو في السادسة من عمره',
      'details':
          '''توفيت أمه آمنة بنت وهب وهو في السادسة من عمره في منطقة الأبواء بين مكة والمدينة، وذلك أثناء عودتها من زيارة أهلها من بني عدي بن النجار في المدينة المنورة.

كانت برفقتها أم أيمن بركة الحبشية، التي تولت رعايته بعد وفاة أمه. فأصبح النبي ﷺ يتيم الأبوين، وتولى رعايته جده عبد المطلب بن هاشم.

دُفنت آمنة بنت وهب في الأبواء، وقد زار النبي ﷺ قبرها بعد سنوات طويلة عندما مر بالأبواء في طريقه لفتح مكة، واستأذن ربه في زيارة قبرها فأُذن له.''',
      'significance':
          'تجربة اليتم التي أثرت في شخصيته الكريمة وجعلته يعطف على الأيتام',
      'location': 'الأبواء (بين مكة والمدينة)',
      'icon': Iconsax.user,
      'color': const Color(0xFF95A5A6),
      'category': 'الحياة المبكرة',
      'keyFigures': ['آمنة بنت وهب', 'أم أيمن بركة', 'عبد المطلب'],
      'relatedEvents': [
        'انتقال الكفالة لجده عبد المطلب',
        'زيارة النبي لقبر أمه لاحقاً'
      ],
      'lessons': [
        'الصبر على البلاء',
        'عطف الله على الأيتام',
        'أهمية الرحمة بالضعفاء'
      ],
    },
    {
      'id': 3,
      'year': '578 م',
      'hijriYear': '3 ق.هـ',
      'age': '8 سنوات',
      'title': 'وفاة جده عبد المطلب',
      'subtitle': 'انتقال الكفالة إلى عمه أبي طالب',
      'description': 'توفي جده عبد المطلب وانتقلت كفالته لعمه أبي طالب',
      'details':
          '''توفي جده عبد المطلب بن هاشم وهو في الثامنة من عمره، وكان عبد المطلب قد أوصى ابنه أبا طالب برعاية محمد ﷺ قبل وفاته.

كان عبد المطلب يحب حفيده محمداً حباً شديداً، وكان يقربه منه ويجلسه على فراشه، ويقول عنه كلاماً يدل على إحساسه بعظمة هذا الطفل.

انتقل النبي ﷺ للعيش مع عمه أبي طالب وزوجته فاطمة بنت أسد، التي عاملته كأحد أبنائها، بل كانت أحن عليه من أبنائها. وقد ظل أبو طالب يحميه ويدافع عنه حتى بعد البعثة.''',
      'significance': 'بداية مرحلة جديدة في حياته تحت رعاية عمه الحنون',
      'location': 'مكة المكرمة',
      'icon': Iconsax.home_2,
      'color': const Color(0xFF34495E),
      'category': 'الحياة المبكرة',
      'keyFigures': ['عبد المطلب', 'أبو طالب', 'فاطمة بنت أسد'],
      'relatedEvents': ['وصية عبد المطلب', 'انتقال للعيش مع أبي طالب'],
      'lessons': [
        'أهمية الأسرة والعشيرة',
        'البر بالأيتام',
        'حفظ الله لأوليائه'
      ],
    },
    {
      'id': 4,
      'year': '583 م',
      'hijriYear': '12 ق.هـ',
      'age': '12 سنة',
      'title': 'رحلة الشام الأولى',
      'subtitle': 'لقاء الراهب بحيرا وشهادته بنبوته',
      'description': 'سافر مع عمه أبي طالب للتجارة في الشام',
      'details':
          '''سافر النبي ﷺ مع عمه أبي طالب في رحلة تجارية إلى الشام وهو في الثانية عشرة من عمره. وفي مدينة بصرى التقوا بالراهب بحيرا، وهو راهب نصراني عالم بالكتب السماوية.

رأى بحيرا علامات النبوة على الفتى محمد، منها الغمامة التي كانت تظلله، وسجود الأشجار والأحجار له، وخاتم النبوة بين كتفيه. فأخبر أبا طالب أن هذا الفتى سيكون نبياً عظيماً.

نصح بحيرا أبا طالب بأن يحذر على ابن أخيه من اليهود، وأن يعود به سريعاً إلى مكة. فعاد أبو طالب بمحمد ﷺ إلى مكة دون إتمام الرحلة التجارية.''',
      'significance': 'أول شهادة خارجية بنبوته قبل البعثة بسنوات طويلة',
      'location': 'بصرى - الشام',
      'icon': Iconsax.airplane,
      'color': const Color(0xFF16A085),
      'category': 'ما قبل البعثة',
      'keyFigures': ['أبو طالب', 'بحيرا الراهب'],
      'relatedEvents': ['شهادة بحيرا', 'ظهور علامات النبوة'],
      'lessons': [
        'علم أهل الكتاب بصفات النبي',
        'حفظ الله لرسوله',
        'ظهور البركات المبكرة'
      ],
    },
    {
      'id': 5,
      'year': '595 م',
      'hijriYear': '25 ق.هـ',
      'age': '25 سنة',
      'title': 'زواجه من خديجة رضي الله عنها',
      'subtitle': 'الزواج المبارك من أم المؤمنين خديجة',
      'description': 'تزوج من خديجة بنت خويلد التاجرة الشريفة',
      'details':
          '''تزوج النبي ﷺ من خديجة بنت خويلد رضي الله عنها وكان عمره 25 سنة وعمرها 40 سنة. كانت خديجة تاجرة ثرية وذات شرف ومكانة عالية في قريش، وكانت تُلقب بالطاهرة.

عملت خديجة مع النبي ﷺ في التجارة، وأُعجبت بأمانته وصدقه وحسن خلقه، فعرضت عليه الزواج عن طريق صديقتها نفيسة بنت منية. وافق النبي ﷺ وتم الزواج بحضور الأهل والأقارب.

عاشا معاً 25 سنة في سعادة ووئام، وأنجبا جميع أولاده ما عدا إبراهيم. كانت خديجة أول من آمن به، وأول من صدقه، وأول من واساه وقت نزول الوحي.''',
      'significance':
          'بداية الاستقرار العائلي والدعم المعنوي الذي احتاجه للرسالة',
      'location': 'مكة المكرمة',
      'icon': Iconsax.heart,
      'color': const Color(0xFFE91E63),
      'category': 'الحياة الشخصية',
      'keyFigures': ['خديجة بنت خويلد', 'نفيسة بنت منية', 'أبو طالب'],
      'relatedEvents': [
        'رحلة التجارة إلى الشام',
        'ولادة الأطفال',
        'بداية الحياة الزوجية'
      ],
      'lessons': [
        'أهمية الزوجة الصالحة',
        'قيمة الدعم المعنوي',
        'بركة الزواج المبارك'
      ],
    },
  ];

  static final List<Map<String, dynamic>> preIslamEvents = [
    {
      'id': 6,
      'year': '605 م',
      'hijriYear': '5 ق.هـ',
      'age': '35 سنة',
      'title': 'حادثة وضع الحجر الأسود',
      'subtitle': 'حكمته في حل النزاع بين قبائل قريش',
      'description': 'حل النزاع حول وضع الحجر الأسود بحكمة',
      'details':
          '''عندما أعادت قريش بناء الكعبة بعد أن هدمها السيل، اختلفت القبائل حول من يحظى بشرف وضع الحجر الأسود في مكانه. كاد الخلاف يؤدي إلى حرب بين القبائل.

اتفقوا على أن يحكم بينهم أول داخل عليهم، فكان النبي ﷺ أول من دخل، فقالوا: "هذا الأمين، رضينا بحكمه". فطلب ثوباً ووضع الحجر عليه، وطلب من كل قبيلة أن تمسك بطرف من الثوب، ثم رفعوه معاً، وهو وضعه في مكانه.

هذا الحل الحكيم أظهر ذكاءه وحكمته، ومنع إراقة الدماء، وأرضى جميع القبائل، وأكد مكانته كحكم عادل بين الناس.''',
      'significance': 'إظهار حكمته وعدالته وقدرته على حل النزاعات قبل البعثة',
      'location': 'الكعبة المشرفة - مكة',
      'icon': Iconsax.award,
      'color': const Color(0xFFF39C12),
      'category': 'ما قبل البعثة',
      'keyFigures': ['قبائل قريش', 'شيوخ مكة'],
      'relatedEvents': ['إعادة بناء الكعبة', 'منع الحرب بين القبائل'],
      'lessons': [
        'الحكمة في حل النزاعات',
        'العدالة والإنصاف',
        'أهمية الحلول الإبداعية'
      ],
    },
  ];

  static final List<Map<String, dynamic>> revelationEvents = [
    {
      'id': 7,
      'year': '610 م',
      'hijriYear': '1 هـ (بداية البعثة)',
      'age': '40 سنة',
      'title': 'بداية الوحي في غار حراء',
      'subtitle': 'نزول أول آيات القرآن الكريم',
      'description': 'نزول الوحي بآيات "اقرأ باسم ربك الذي خلق"',
      'details':
          '''في شهر رمضان المبارك من عام 610م، نزل الوحي على النبي ﷺ لأول مرة في غار حراء بجبل النور. كان النبي ﷺ يتعبد في هذا الغار ويتفكر في خلق الله وحال قومه.

جاءه جبريل عليه السلام وقال له: "اقرأ"، فقال النبي ﷺ: "ما أنا بقارئ"، فغطه جبريل حتى بلغ منه الجهد، ثم أرسله وقال: "اقرأ"، فقال: "ما أنا بقارئ"، فغطه الثانية والثالثة، ثم قال: "اقرأ باسم ربك الذي خلق، خلق الإنسان من علق، اقرأ وربك الأكرم، الذي علم بالقلم، علم الإنسان ما لم يعلم".

عاد النبي ﷺ إلى خديجة يرجف فؤاده، فأخبرها بما حدث، فطمأنته وقالت: "كلا والله لا يخزيك الله أبداً، إنك لتصل الرحم، وتحمل الكل، وتكسب المعدوم، وتقري الضيف، وتعين على نوائب الحق".''',
      'significance': 'بداية الرسالة الخاتمة للبشرية وتغيير مجرى التاريخ',
      'location': 'غار حراء - جبل النور - مكة',
      'icon': Iconsax.book_1,
      'color': const Color(0xFF9B59B6),
      'category': 'الرسالة',
      'keyFigures': ['جبريل عليه السلام', 'خديجة رضي الله عنها'],
      'relatedEvents': ['أول آيات القرآن', 'تطمين خديجة', 'بداية الدعوة'],
      'lessons': [
        'عظمة القرآن الكريم',
        'أهمية الزوجة المؤمنة',
        'بداية الهداية للبشرية'
      ],
    },
    {
      'id': 8,
      'year': '613 م',
      'hijriYear': '4 هـ',
      'age': '43 سنة',
      'title': 'الدعوة الجهرية',
      'subtitle': 'بداية الدعوة العلنية للإسلام',
      'description':
          'أمر الله نبيه بالجهر بالدعوة بعد ثلاث سنوات من الدعوة السرية',
      'details':
          '''بعد ثلاث سنوات من الدعوة السرية، أمر الله نبيه بالجهر بالدعوة، فنزلت آية: "وأنذر عشيرتك الأقربين". صعد النبي ﷺ على جبل الصفا ونادى قبائل قريش: "يا صباحاه"، فاجتمعوا إليه.

قال لهم: "أرأيتكم لو أخبرتكم أن خيلاً بالوادي تريد أن تغير عليكم، أكنتم مصدقي؟" قالوا: "نعم، ما جربنا عليك إلا صدقاً". قال: "فإني نذير لكم بين يدي عذاب شديد".

فقال أبو لهب: "تباً لك سائر اليوم، ألهذا جمعتنا؟" فنزلت سورة المسد: "تبت يدا أبي لهب وتب". من هذا اليوم بدأت المعارضة الشديدة من قريش، وبدأ الأذى والاضطهاد للمسلمين.''',
      'significance':
          'انطلاق الدعوة الإسلامية علناً وبداية المواجهة مع الجاهلية',
      'location': 'جبل الصفا - مكة المكرمة',
      'icon': Iconsax.microphone,
      'color': const Color(0xFFE74C3C),
      'category': 'الرسالة',
      'keyFigures': ['قبائل قريش', 'أبو لهب', 'المسلمون الأوائل'],
      'relatedEvents': [
        'نزول سورة المسد',
        'بداية الاضطهاد',
        'إسلام أبي بكر وعلي'
      ],
      'lessons': ['الشجاعة في قول الحق', 'مواجهة الباطل', 'الثبات على المبدأ'],
    },
    {
      'id': 9,
      'year': '615 م',
      'hijriYear': '6 هـ',
      'age': '45 سنة',
      'title': 'الهجرة الأولى إلى الحبشة',
      'subtitle': 'هجرة المسلمين الأولى فراراً من الاضطهاد',
      'description': 'هاجر مجموعة من المسلمين إلى الحبشة بأمر النبي ﷺ',
      'details':
          '''لما اشتد الأذى على المسلمين في مكة، أمر النبي ﷺ أصحابه بالهجرة إلى الحبشة، وقال لهم: "إن بها ملكاً لا يُظلم عنده أحد، وهي أرض صدق، حتى يجعل الله لكم فرجاً مما أنتم فيه".

هاجر في هذه المرة الأولى حوالي 15 رجلاً و4 نساء، بقيادة عثمان بن عفان وزوجته رقية بنت النبي ﷺ. استقبلهم النجاشي ملك الحبشة بالترحاب وحماهم من قريش.

لما علمت قريش بذلك، أرسلت عمرو بن العاص وعبد الله بن أبي ربيعة بالهدايا للنجاشي ليسلمهم المسلمين، لكن النجاشي رفض بعد أن سمع من جعفر بن أبي طالب عن الإسلام وتلا عليه آيات من سورة مريم.''',
      'significance': 'أول هجرة في الإسلام وانتشار الدعوة خارج الجزيرة العربية',
      'location': 'الحبشة (إثيوبيا حالياً)',
      'icon': Iconsax.airplane,
      'color': const Color(0xFF27AE60),
      'category': 'الرسالة',
      'keyFigures': [
        'عثمان بن عفان',
        'رقية بنت النبي',
        'جعفر بن أبي طالب',
        'النجاشي'
      ],
      'relatedEvents': [
        'محاولة قريش استرداد المهاجرين',
        'إسلام النجاشي',
        'تلاوة آيات مريم'
      ],
      'lessons': [
        'الهجرة في سبيل الله',
        'العدالة عند غير المسلمين',
        'انتشار الإسلام'
      ],
    },
  ];

  static final List<Map<String, dynamic>> migrationEvents = [
    {
      'id': 10,
      'year': '622 م',
      'hijriYear': '1 هـ',
      'age': '53 سنة',
      'title': 'الهجرة النبوية المباركة',
      'subtitle': 'الانتقال التاريخي من مكة إلى المدينة المنورة',
      'description': 'هاجر النبي ﷺ مع أبي بكر من مكة إلى المدينة',
      'details':
          '''في عام 622م، هاجر النبي ﷺ مع صاحبه أبي بكر الصديق من مكة إلى المدينة المنورة، هرباً من أذى قريش وتآمرهم على قتله. خرجا ليلاً واختبآ في غار ثور ثلاثة أيام.

أرسلت قريش الفرسان للبحث عنهما، ووصلوا إلى الغار، لكن الله حماهما بمعجزات عديدة منها نسج العنكبوت على فم الغار وبناء الحمامة عشها. قال أبو بكر: "يا رسول الله، لو أن أحدهم نظر إلى قدميه لأبصرنا تحت قدميه"، فقال النبي ﷺ: "يا أبا بكر، ما ظنك باثنين الله ثالثهما؟".

وصلا إلى المدينة بعد رحلة استغرقت عدة أيام، واستقبلهما أهل المدينة بالترحاب والفرح، وهم ينشدون: "طلع البدر علينا من ثنيات الوداع". هذا الحدث بداية التاريخ الهجري والدولة الإسلامية الأولى.''',
      'significance':
          'بداية الدولة الإسلامية الأولى وانطلاق الإسلام كقوة سياسية',
      'location': 'من مكة إلى المدينة المنورة',
      'icon': Iconsax.location,
      'color': const Color(0xFF3498DB),
      'category': 'الهجرة والدولة',
      'keyFigures': ['أبو بكر الصديق', 'أهل المدينة', 'علي بن أبي طالب'],
      'relatedEvents': [
        'الاختباء في غار ثور',
        'معجزة العنكبوت',
        'استقبال أهل المدينة'
      ],
      'lessons': ['التوكل على الله', 'الصحبة الصالحة', 'بداية التمكين'],
    },
  ];

  static final List<Map<String, dynamic>> conquestEvents = [
    {
      'id': 11,
      'year': '629 م',
      'hijriYear': '8 هـ',
      'age': '59 سنة',
      'title': 'صلح الحديبية',
      'subtitle': 'معاهدة السلام التاريخية مع قريش',
      'description': 'وقع النبي ﷺ صلحاً مع قريش سماه الله فتحاً مبيناً',
      'details':
          '''في ذي القعدة من السنة السادسة للهجرة، خرج النبي ﷺ مع 1400 من أصحابه قاصدين العمرة، لكن قريش منعتهم من دخول مكة. فأرسلت قريش سهيل بن عمرو للتفاوض.

تم التوقيع على معاهدة الحديبية التي نصت على: وقف القتال 10 سنوات، عودة المسلمين هذا العام على أن يعتمروا العام القادم، إرجاع من يأتي من قريش مسلماً، وعدم إرجاع من يذهب من المسلمين لقريش.

رغم أن الصحابة رأوها هدنة مجحفة، إلا أن الله سماها في القرآن "فتحاً مبيناً". وقد ثبت أنها كانت فتحاً حقيقياً، فقد دخل في الإسلام خلال السنتين التاليتين أكثر ممن دخل في السنوات السابقة كلها.''',
      'significance':
          'دبلوماسية نبوية حكيمة غيرت مجرى الأحداث وفتحت الطريق لانتشار الإسلام',
      'location': 'الحديبية (قرب مكة)',
      'icon': Iconsax.people,
      'color': const Color(0xFFF39C12),
      'category': 'الفتوحات',
      'keyFigures': [
        'سهيل بن عمرو',
        'أبو بكر',
        'عمر بن الخطاب',
        'علي بن أبي طالب'
      ],
      'relatedEvents': [
        'بيعة الرضوان',
        'إسلام خالد بن الوليد',
        'إسلام عمرو بن العاص'
      ],
      'lessons': [
        'الحكمة في التفاوض',
        'النظرة بعيدة المدى',
        'الصبر على النتائج'
      ],
    },
    {
      'id': 12,
      'year': '630 م',
      'hijriYear': '8 هـ',
      'age': '60 سنة',
      'title': 'فتح مكة المكرمة',
      'subtitle': 'الفتح المبين والعفو العام',
      'description': 'فتح النبي ﷺ مكة فتحاً مبيناً وأعلن العفو العام',
      'details':
          '''في رمضان من السنة الثامنة للهجرة، نقضت قريش صلح الحديبية بمساعدة بني بكر ضد خزاعة حلفاء المسلمين. فخرج النبي ﷺ في عشرة آلاف مقاتل لفتح مكة.

دخل النبي ﷺ مكة من أربعة جهات، وأمر قادته بعدم القتال إلا للدفاع. استسلمت مكة دون قتال يُذكر، ودخل النبي ﷺ المسجد الحرام وطاف بالكعبة وكسر الأصنام التي حولها، وهو يتلو: "وقل جاء الحق وزهق الباطل إن الباطل كان زهوقاً".

جمع النبي ﷺ أهل مكة وقال لهم: "ما تظنون أني فاعل بكم؟" قالوا: "خيراً، أخ كريم وابن أخ كريم". فقال: "اذهبوا فأنتم الطلقاء". هذا العفو العام أدى إلى دخول معظم أهل مكة في الإسلام.''',
      'significance':
          'انتصار الإسلام وتطهير البيت الحرام وبداية انهيار الوثنية في الجزيرة',
      'location': 'مكة المكرمة',
      'icon': Iconsax.flag,
      'color': const Color(0xFF8E44AD),
      'category': 'الفتوحات',
      'keyFigures': [
        'خالد بن الوليد',
        'سعد بن أبي وقاص',
        'الزبير بن العوام',
        'أبو عبيدة'
      ],
      'relatedEvents': [
        'كسر الأصنام',
        'العفو العام',
        'إسلام أهل مكة',
        'تطهير الكعبة'
      ],
      'lessons': [
        'العفو عند المقدرة',
        'انتصار الحق على الباطل',
        'الرحمة في النصر'
      ],
    },
  ];

  static final List<Map<String, dynamic>> finalEvents = [
    {
      'id': 13,
      'year': '632 م',
      'hijriYear': '10 هـ',
      'age': '63 سنة',
      'title': 'حجة الوداع والوفاة',
      'subtitle': 'آخر حج وانتقال النبي ﷺ إلى الرفيق الأعلى',
      'description': 'أدى النبي ﷺ حجة الوداع وألقى خطبته الشهيرة ثم توفي',
      'details':
          '''في ذي الحجة من السنة العاشرة للهجرة، أدى النبي ﷺ حجة الوداع مع أكثر من مئة ألف مسلم. ألقى خطبته الشهيرة في عرفة، والتي تضمنت أسس العدالة والمساواة وحقوق الإنسان.

قال في خطبته: "أيها الناس، إن دماءكم وأموالكم وأعراضكم عليكم حرام كحرمة يومكم هذا في شهركم هذا في بلدكم هذا... وإنكم ستلقون ربكم فيسألكم عن أعمالكم، ألا فلا ترجعوا بعدي كفاراً يضرب بعضكم رقاب بعض".

نزلت آية: "اليوم أكملت لكم دينكم وأتممت عليكم نعمتي ورضيت لكم الإسلام ديناً". وفي ربيع الأول من السنة الحادية عشرة، مرض النبي ﷺ وتوفي في بيت عائشة، ودُفن في المدينة المنورة.''',
      'significance': 'ختام الرسالة وانتقال القيادة للأمة وإكمال الدين',
      'location': 'مكة والمدينة المنورة',
      'icon': Iconsax.home_2,
      'color': const Color(0xFF34495E),
      'category': 'الختام',
      'keyFigures': ['عائشة رضي الله عنها', 'أبو بكر الصديق', 'المسلمون'],
      'relatedEvents': [
        'خطبة الوداع',
        'نزول آية إكمال الدين',
        'وفاة النبي',
        'دفنه في المدينة'
      ],
      'lessons': [
        'إكمال الدين',
        'المساواة بين الناس',
        'انتقال المسؤولية للأمة'
      ],
    },
  ];

  static List<Map<String, dynamic>> getAllEvents() {
    List<Map<String, dynamic>> allEvents = [];
    allEvents.addAll(timelineEvents);
    allEvents.addAll(preIslamEvents);
    allEvents.addAll(revelationEvents);
    allEvents.addAll(migrationEvents);
    allEvents.addAll(conquestEvents);
    allEvents.addAll(finalEvents);
    return allEvents..sort((a, b) => a['id'].compareTo(b['id']));
  }

  static List<String> getCategories() {
    return [
      'الكل',
      'الحياة المبكرة',
      'ما قبل البعثة',
      'الرسالة',
      'الهجرة والدولة',
      'الفتوحات',
      'الختام',
    ];
  }

  static List<Map<String, dynamic>> getEventsByCategory(String category) {
    if (category == 'الكل') {
      return getAllEvents();
    }
    return getAllEvents()
        .where((event) => event['category'] == category)
        .toList();
  }
}
