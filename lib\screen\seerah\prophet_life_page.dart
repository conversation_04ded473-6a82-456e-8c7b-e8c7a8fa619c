import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';

class ProphetLifePage extends StatefulWidget {
  const ProphetLifePage({super.key});

  @override
  State<ProphetLifePage> createState() => _ProphetLifePageState();
}

class _ProphetLifePageState extends State<ProphetLifePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int selectedCategoryIndex = 0;

  final List<Map<String, dynamic>> categories = [
    {
      'title': 'الأخلاق النبوية',
      'icon': Iconsax.heart,
      'color': const Color(0xFF27AE60),
      'gradient': [const Color(0xFF27AE60), const Color(0xFF2ECC71)],
    },
    {
      'title': 'الصفات الجسدية',
      'icon': Iconsax.user,
      'color': const Color(0xFF3498DB),
      'gradient': [const Color(0xFF3498DB), const Color(0xFF5DADE2)],
    },
    {
      'title': 'الحياة اليومية',
      'icon': Iconsax.home_2,
      'color': const Color(0xFF9B59B6),
      'gradient': [const Color(0xFF9B59B6), const Color(0xFFBB8FCE)],
    },
    {
      'title': 'المعجزات',
      'icon': Iconsax.star,
      'color': const Color(0xFFE74C3C),
      'gradient': [const Color(0xFFE74C3C), const Color(0xFFEC7063)],
    },
    {
      'title': 'الأسماء والألقاب',
      'icon': Iconsax.crown,
      'color': const Color(0xFFF39C12),
      'gradient': [const Color(0xFFF39C12), const Color(0xFFF7DC6F)],
    },
    {
      'title': 'العبادة والذكر',
      'icon': Iconsax.book_1,
      'color': const Color(0xFF8E44AD),
      'gradient': [const Color(0xFF8E44AD), const Color(0xFFAB7FB4)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFB),
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildHeroSection(),
                const SizedBox(height: 24),
                _buildCategoriesGrid(),
                const SizedBox(height: 24),
                _buildSelectedCategoryContent(),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Colors.white,
      elevation: 0,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(28),
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'حياة خير البشر محمد ﷺ',
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1A202C),
          ),
        ),
        centerTitle: true,
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                const Color(0xFF27AE60).withValues(alpha: 0.05),
              ],
            ),
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(28),
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -30,
                top: -20,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF27AE60).withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -40,
                bottom: -30,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF3498DB).withValues(alpha: 0.08),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF27AE60).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.arrow_back_ios_rounded,
            color: Color(0xFF27AE60),
            size: 18,
          ),
        ),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF27AE60).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Iconsax.heart,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'محمد رسول الله ﷺ',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'خاتم الأنبياء والمرسلين • الرحمة المهداة • الصادق الأمين',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '"وَمَا أَرْسَلْنَاكَ إِلَّا رَحْمَةً لِّلْعَالَمِينَ"',
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'استكشف جوانب حياة النبي ﷺ',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1A202C),
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = selectedCategoryIndex == index;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedCategoryIndex = index;
                  });
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: category['gradient'],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    color: isSelected ? null : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: isSelected
                        ? null
                        : Border.all(
                            color: (category['color'] as Color)
                                .withValues(alpha: 0.2),
                            width: 1,
                          ),
                    boxShadow: [
                      BoxShadow(
                        color: isSelected
                            ? (category['color'] as Color)
                                .withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.05),
                        blurRadius: isSelected ? 15 : 10,
                        offset: Offset(0, isSelected ? 8 : 4),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.white.withValues(alpha: 0.2)
                                : (category['color'] as Color)
                                    .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            category['icon'],
                            size: 28,
                            color:
                                isSelected ? Colors.white : category['color'],
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          category['title'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF1A202C),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedCategoryContent() {
    final category = categories[selectedCategoryIndex];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: category['gradient'],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  category['icon'],
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  category['title'],
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1A202C),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildCategoryContent(selectedCategoryIndex),
        ],
      ),
    );
  }

  Widget _buildCategoryContent(int categoryIndex) {
    switch (categoryIndex) {
      case 0: // الأخلاق النبوية
        return _buildMoralsContent();
      case 1: // الصفات الجسدية
        return _buildPhysicalTraitsContent();
      case 2: // الحياة اليومية
        return _buildDailyLifeContent();
      case 3: // المعجزات
        return _buildMiraclesContent();
      case 4: // الأسماء والألقاب
        return _buildNamesContent();
      case 5: // العبادة والذكر
        return _buildWorshipContent();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildMoralsContent() {
    final morals = [
      {
        'title': 'الصدق والأمانة',
        'description': 'كان يُلقب بالصادق الأمين قبل البعثة',
        'details':
            'لم يُعرف عنه ﷺ كذبة واحدة طوال حياته، حتى أعداؤه شهدوا بصدقه وأمانته. كانت قريش تودع عنده أماناتها.',
        'icon': Iconsax.shield_tick,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'الرحمة والرأفة',
        'description': 'رحمة للعالمين كما وصفه الله',
        'details':
            'كان رحيماً بالصغير والكبير، بالإنسان والحيوان. عفا عن أهل مكة يوم الفتح رغم إيذائهم له.',
        'icon': Iconsax.heart,
        'color': const Color(0xFFE74C3C),
      },
      {
        'title': 'التواضع والبساطة',
        'description': 'أبسط الناس رغم عظمته',
        'details':
            'كان يجلس مع الفقراء، ويأكل على الأرض، ويخصف نعله بيده، ويقول: "إنما أنا عبد آكل كما يأكل العبد".',
        'icon': Iconsax.user,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'الحلم والصبر',
        'description': 'صبر على الأذى وحلم على الجاهلين',
        'details':
            'صبر على أذى قومه 13 سنة في مكة، وحلم على الأعرابي الذي جذبه من ردائه حتى أثر في عنقه.',
        'icon': Iconsax.timer,
        'color': const Color(0xFF9B59B6),
      },
      {
        'title': 'العدل والإنصاف',
        'description': 'عدل حتى مع أعدائه',
        'details':
            'قال: "لو أن فاطمة بنت محمد سرقت لقطعت يدها". كان عادلاً في أحكامه لا يحابي أحداً.',
        'icon': Iconsax.weight,
        'color': const Color(0xFFF39C12),
      },
      {
        'title': 'الكرم والجود',
        'description': 'أجود الناس بالخير',
        'details':
            'كان أجود بالخير من الريح المرسلة، لا يُسأل شيئاً إلا أعطاه، وكان يعطي عطاء من لا يخشى الفقر.',
        'icon': Iconsax.gift,
        'color': const Color(0xFF1ABC9C),
      },
    ];

    return Column(
      children: morals.map((moral) => _buildMoralCard(moral)).toList(),
    );
  }

  Widget _buildMoralCard(Map<String, dynamic> moral) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (moral['color'] as Color).withValues(alpha: 0.05),
            (moral['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (moral['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (moral['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  moral['icon'],
                  color: moral['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      moral['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      moral['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            moral['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhysicalTraitsContent() {
    final traits = [
      {
        'title': 'الطول والقامة',
        'description': 'ربعة ليس بالطويل ولا بالقصير',
        'details':
            'كان ﷺ مربوع القامة، إذا مشى مع الطوال طالهم، وإذا جلس كان أطول من الجالسين.',
        'icon': Iconsax.ruler,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'الوجه الشريف',
        'description': 'أحسن الناس وجهاً',
        'details':
            'كان وجهه مستديراً مشرباً بحمرة، أزهر اللون، كأن الشمس تجري في وجهه، لم يُر قبله ولا بعده مثله.',
        'icon': Iconsax.user,
        'color': const Color(0xFFE74C3C),
      },
      {
        'title': 'الشعر المبارك',
        'description': 'شعر أسود مموج',
        'details':
            'كان شعره أسود، ليس بجعد قطط ولا سبط، يصل إلى شحمة أذنيه، وكان يدهنه ويرجله.',
        'icon': Iconsax.brush_1,
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'العينان الكريمتان',
        'description': 'عيون واسعة جميلة',
        'details':
            'كان أشكل العينين، واسع الأعين، أهدب الأشفار، إذا نظر إليك ظننت أنه ينظر إليك وحدك.',
        'icon': Iconsax.eye,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'الصوت الشريف',
        'description': 'أحسن الناس صوتاً',
        'details':
            'كان حسن الصوت، إذا تكلم أطرق جلساؤه كأنما على رؤوسهم الطير، وكان يتكلم بجوامع الكلم.',
        'icon': Iconsax.microphone,
        'color': const Color(0xFFF39C12),
      },
    ];

    return Column(
      children: traits.map((trait) => _buildTraitCard(trait)).toList(),
    );
  }

  Widget _buildTraitCard(Map<String, dynamic> trait) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (trait['color'] as Color).withValues(alpha: 0.05),
            (trait['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (trait['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (trait['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  trait['icon'],
                  color: trait['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      trait['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      trait['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            trait['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyLifeContent() {
    final dailyLife = [
      {
        'title': 'الطعام والشراب',
        'description': 'بساطة في الأكل وآداب عظيمة',
        'details':
            'كان يأكل مما يليه، ويسمي الله، ويأكل بثلاث أصابع، ولا يعيب طعاماً قط، إن اشتهاه أكله وإلا تركه.',
        'icon': Iconsax.cup,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'النوم والراحة',
        'description': 'نوم معتدل مع ذكر الله',
        'details':
            'كان ينام على جنبه الأيمن، ويضع يده تحت خده، ويقرأ آية الكرسي والمعوذات قبل النوم.',
        'icon': Iconsax.moon,
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'اللباس والزينة',
        'description': 'بساطة مع نظافة',
        'details':
            'كان يحب الثوب الأبيض، ويتطيب، ويسرح شعره ولحيته، وكان أطيب الناس ريحاً.',
        'icon': Iconsax.personalcard,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'المعاملة مع الأهل',
        'description': 'خير الناس لأهله',
        'details':
            'كان خير الناس لأهله، يساعد في البيت، ويخصف نعله، ويرقع ثوبه، ويحلب شاته.',
        'icon': Iconsax.home_2,
        'color': const Color(0xFFE74C3C),
      },
    ];

    return Column(
      children: dailyLife.map((item) => _buildDailyLifeCard(item)).toList(),
    );
  }

  Widget _buildDailyLifeCard(Map<String, dynamic> item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (item['color'] as Color).withValues(alpha: 0.05),
            (item['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (item['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (item['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  item['icon'],
                  color: item['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      item['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            item['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiraclesContent() {
    final miracles = [
      {
        'title': 'القرآن الكريم',
        'description': 'المعجزة الخالدة',
        'details':
            'القرآن الكريم هو المعجزة الكبرى، تحدى الله به العرب وهم أهل البلاغة والفصاحة فعجزوا عن الإتيان بمثله.',
        'icon': Iconsax.book_1,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'الإسراء والمعراج',
        'description': 'الرحلة المباركة',
        'details':
            'أُسري بالنبي ﷺ ليلاً من المسجد الحرام إلى المسجد الأقصى، ثم عُرج به إلى السماوات العلى.',
        'icon': Iconsax.airplane,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'انشقاق القمر',
        'description': 'آية عظيمة في السماء',
        'details':
            'انشق القمر نصفين بإشارة من النبي ﷺ، رآه أهل مكة جميعاً، وهي من الآيات الباهرات.',
        'icon': Iconsax.moon,
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'تكثير الطعام',
        'description': 'إطعام الكثير من القليل',
        'details':
            'أطعم ﷺ الآلاف من طعام قليل في مواقف عديدة، كإطعام ألف رجل من شاة واحدة.',
        'icon': Iconsax.cup,
        'color': const Color(0xFFE74C3C),
      },
      {
        'title': 'نبع الماء',
        'description': 'الماء من بين أصابعه',
        'details':
            'نبع الماء من بين أصابعه الشريفة، وشرب منه الصحابة وتوضؤوا، وكانوا أكثر من ألف رجل.',
        'icon': Iconsax.drop,
        'color': const Color(0xFF1ABC9C),
      },
    ];

    return Column(
      children: miracles.map((miracle) => _buildMiracleCard(miracle)).toList(),
    );
  }

  Widget _buildMiracleCard(Map<String, dynamic> miracle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (miracle['color'] as Color).withValues(alpha: 0.05),
            (miracle['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (miracle['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (miracle['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  miracle['icon'],
                  color: miracle['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      miracle['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      miracle['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            miracle['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNamesContent() {
    final names = [
      {
        'title': 'محمد',
        'description': 'الاسم الأشهر - المحمود',
        'details':
            'سماه جده عبد المطلب محمداً، وهو اسم لم يكن معروفاً في العرب، ومعناه كثير الحمد.',
        'icon': Iconsax.crown,
        'color': const Color(0xFFF39C12),
      },
      {
        'title': 'أحمد',
        'description': 'المذكور في الإنجيل',
        'details':
            'بشر به عيسى عليه السلام في الإنجيل، ومعناه الذي يحمد الله أكثر من غيره.',
        'icon': Iconsax.star,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'الصادق الأمين',
        'description': 'لقبه قبل البعثة',
        'details':
            'لقبته قريش بهذا اللقب لصدقه وأمانته، حتى أعداؤه كانوا يودعون عنده أماناتهم.',
        'icon': Iconsax.shield_tick,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'خاتم النبيين',
        'description': 'آخر الأنبياء والمرسلين',
        'details':
            'ختم الله به النبوة، فلا نبي بعده، وهو المبعوث للناس كافة إلى يوم القيامة.',
        'icon': Iconsax.medal,
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'رسول الله',
        'description': 'المرسل من عند الله',
        'details':
            'أرسله الله رحمة للعالمين، بالهدى ودين الحق ليظهره على الدين كله.',
        'icon': Iconsax.send_1,
        'color': const Color(0xFFE74C3C),
      },
    ];

    return Column(
      children: names.map((name) => _buildNameCard(name)).toList(),
    );
  }

  Widget _buildNameCard(Map<String, dynamic> name) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (name['color'] as Color).withValues(alpha: 0.05),
            (name['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (name['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (name['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  name['icon'],
                  color: name['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      name['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            name['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorshipContent() {
    final worship = [
      {
        'title': 'الصلاة',
        'description': 'قرة عينه وراحة قلبه',
        'details':
            'كان يقول: "جُعلت قرة عيني في الصلاة"، وكان يصلي حتى تتفطر قدماه، ويقوم الليل حتى الفجر.',
        'icon': Iconsax.user,
        'color': const Color(0xFF27AE60),
      },
      {
        'title': 'القرآن والذكر',
        'description': 'تلاوة وتدبر وذكر',
        'details':
            'كان يقرأ القرآن بصوت حسن، ويذكر الله في كل أحواله، ويسبح الله مئات المرات يومياً.',
        'icon': Iconsax.book_1,
        'color': const Color(0xFF3498DB),
      },
      {
        'title': 'الصيام',
        'description': 'صيام التطوع والنوافل',
        'details':
            'كان يصوم الاثنين والخميس، وثلاثة أيام من كل شهر، وعاشوراء، وعرفة، وكان يعتكف في رمضان.',
        'icon': Iconsax.moon,
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'الدعاء والاستغفار',
        'description': 'دعاء دائم واستغفار كثير',
        'details':
            'كان يستغفر الله في اليوم أكثر من مئة مرة، ويدعو بجوامع الدعاء، ويطيل الدعاء في السجود.',
        'icon': Iconsax.heart,
        'color': const Color(0xFFE74C3C),
      },
      {
        'title': 'الحج والعمرة',
        'description': 'حج مرة واعتمر أربع مرات',
        'details':
            'حج حجة واحدة هي حجة الوداع، واعتمر أربع عمرات، وعلم الناس مناسك الحج والعمرة.',
        'icon': Iconsax.home_2,
        'color': const Color(0xFF1ABC9C),
      },
    ];

    return Column(
      children: worship.map((item) => _buildWorshipCard(item)).toList(),
    );
  }

  Widget _buildWorshipCard(Map<String, dynamic> item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (item['color'] as Color).withValues(alpha: 0.05),
            (item['color'] as Color).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (item['color'] as Color).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (item['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  item['icon'],
                  color: item['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1A202C),
                      ),
                    ),
                    Text(
                      item['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            item['details'],
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
