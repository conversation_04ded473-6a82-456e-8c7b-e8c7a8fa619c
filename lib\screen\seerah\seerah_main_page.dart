import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';
import 'package:wiffada/screen/seerah/timeline_page.dart';
import 'package:wiffada/screen/seerah/companions_page.dart';
import 'package:wiffada/screen/seerah/interactive_events_page.dart';

class SeerahMainPage extends StatefulWidget {
  const SeerahMainPage({Key? key}) : super(key: key);

  @override
  State<SeerahMainPage> createState() => _SeerahMainPageState();
}

class _SeerahMainPageState extends State<SeerahMainPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildHeroSection(),
                const SizedBox(height: 20),
                _buildFeaturesGrid(),
                const SizedBox(height: 20),
                _buildStatsSection(),
                const SizedBox(height: 20),
                _buildQuickAccessSection(),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF4F908E),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'السيرة النبوية التفاعلية',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF4F908E),
                const Color(0xFF4F908E).withOpacity(0.8),
              ],
            ),
          ),
          child: Center(
            child: Icon(
              Iconsax.book,
              size: 60,
              color: Colors.white.withOpacity(0.3),
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4F908E).withOpacity(0.1),
            const Color(0xFF4F908E).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4F908E).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Iconsax.book_1,
            size: 80,
            color: const Color(0xFF4F908E),
          ).animate().scale(duration: const Duration(milliseconds: 600)),
          const SizedBox(height: 16),
          Text(
            'تعلم السيرة النبوية بطريقة تفاعلية',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C3E50),
            ),
            textAlign: TextAlign.center,
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
          const SizedBox(height: 12),
          Text(
            'اكتشف حياة النبي محمد ﷺ من خلال تجربة تعليمية غامرة تجمع بين التكنولوجيا والمعرفة الدينية',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
        ],
      ),
    );
  }

  Widget _buildFeaturesGrid() {
    final features = [
      {
        'title': 'الخط الزمني التفاعلي',
        'description': 'تتبع الأحداث المهمة في حياة النبي ﷺ',
        'icon': Iconsax.calendar,
        'color': const Color(0xFF3498DB),
        'page': const TimelinePage(),
      },
      {
        'title': 'معرض الصحابة',
        'description': 'تعرف على أصحاب النبي ﷺ وقصصهم',
        'icon': Iconsax.people,
        'color': const Color(0xFF9B59B6),
        'page': const CompanionsPage(),
      },
      {
        'title': 'الأحداث التفاعلية',
        'description': 'عش الأحداث التاريخية بتقنية ثلاثية الأبعاد',
        'icon': Iconsax.video_play,
        'color': const Color(0xFFE74C3C),
        'page': const InteractiveEventsPage(),
      },
      {
        'title': 'الاختبارات والألعاب',
        'description': 'اختبر معلوماتك واكسب النقاط',
        'icon': Iconsax.game,
        'color': const Color(0xFFF39C12),
        'page': null, // سيتم إضافتها لاحقاً
      },
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: features.length,
        itemBuilder: (context, index) {
          final feature = features[index];
          return _buildFeatureCard(feature, index);
        },
      ),
    );
  }

  Widget _buildFeatureCard(Map<String, dynamic> feature, int index) {
    return GestureDetector(
      onTap: () {
        if (feature['page'] != null) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => feature['page']),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'قريباً... ${feature['title']}',
                style: GoogleFonts.ibmPlexSansArabic(),
              ),
              backgroundColor: const Color(0xFF4F908E),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              offset: const Offset(0, 4),
              blurRadius: 20,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: (feature['color'] as Color).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  feature['icon'],
                  size: 32,
                  color: feature['color'],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                feature['title'],
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                feature['description'],
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 12,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideY(begin: 0.3, end: 0);
  }

  Widget _buildStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 4),
            blurRadius: 15,
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'إحصائيات التعلم',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('50+', 'حدث تاريخي', Iconsax.calendar),
              _buildStatItem('100+', 'صحابي وصحابية', Iconsax.people),
              _buildStatItem('20+', 'مشهد تفاعلي', Iconsax.video_play),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String number, String label, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 32,
          color: const Color(0xFF4F908E),
        ),
        const SizedBox(height: 8),
        Text(
          number,
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF4F908E),
          ),
        ),
        Text(
          label,
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQuickAccessSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الوصول السريع',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          _buildQuickAccessCard(
            'ابدأ رحلة التعلم',
            'ابدأ من البداية واتبع الخط الزمني',
            Iconsax.play_circle,
            const Color(0xFF27AE60),
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const TimelinePage()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(0, 2),
              blurRadius: 10,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 24,
                color: color,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
