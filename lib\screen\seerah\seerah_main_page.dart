import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';
import 'package:wiffada/screen/seerah/timeline_page.dart';
import 'package:wiffada/screen/seerah/prophet_life_page.dart';

class SeerahMainPage extends StatefulWidget {
  const SeerahMainPage({super.key});

  @override
  State<SeerahMainPage> createState() => _SeerahMainPageState();
}

class _SeerahMainPageState extends State<SeerahMainPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildMainContent(),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF4F908E),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'السيرة النبوية التفاعلية',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF4F908E),
                const Color(0xFF4F908E).withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Center(
            child: Icon(
              Iconsax.book,
              size: 60,
              color: Colors.white.withValues(alpha: 0.3),
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9B59B6), Color(0xFF8E44AD)],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Iconsax.book_1,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'السيرة النبوية الشريفة',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1A202C),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'تعرف على حياة خير البشر محمد ﷺ',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // الأقسام الرئيسية
          _buildSeerahSections(),
        ],
      ),
    );
  }

  Widget _buildSeerahSections() {
    final sections = [
      {
        'title': 'الخط الزمني للسيرة النبوية',
        'description': 'رحلة شاملة عبر حياة النبي محمد ﷺ من الولادة حتى الوفاة',
        'details':
            'من ولادته الشريفة في مكة المكرمة، مرورًا ببعثته ونزول الوحي، والهجرة المباركة، وحتى وفاته ﷺ',
        'icon': Iconsax.calendar,
        'color': const Color(0xFF2E86AB),
        'page': const TimelinePage(),
        'features': [
          '63 عامًا من الحياة الشريفة',
          '40 عامًا قبل البعثة',
          '23 عامًا من الرسالة'
        ],
      },
      {
        'title': 'حياة خير البشر محمد ﷺ',
        'description': 'تعرف على شخصية النبي الكريم وأخلاقه وصفاته العظيمة',
        'details':
            'اكتشف أخلاق النبي ﷺ، وصفاته الجسدية والخُلقية، وحياته اليومية، ومعاملته مع الناس',
        'icon': Iconsax.heart,
        'color': const Color(0xFF27AE60),
        'page': const ProphetLifePage(),
        'features': ['الأخلاق النبوية', 'الصفات الشريفة', 'الحياة اليومية'],
      },
    ];

    return Column(
      children: sections
          .map((section) => _buildEnhancedSectionCard(section))
          .toList(),
    );
  }

  Widget _buildEnhancedSectionCard(Map<String, dynamic> section) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: (section['color'] as Color).withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (section['page'] != null) {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => section['page']),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'قريباً... ${section['title']}',
                    style: GoogleFonts.ibmPlexSansArabic(),
                    textAlign: TextAlign.center,
                  ),
                  backgroundColor: section['color'],
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );
            }
          },
          borderRadius: BorderRadius.circular(24),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الرأس مع الأيقونة والعنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            section['color'],
                            (section['color'] as Color).withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: (section['color'] as Color)
                                .withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Icon(
                        section['icon'],
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            section['title'],
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF1A202C),
                              height: 1.3,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            section['description'],
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 14,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // التفاصيل
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (section['color'] as Color).withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (section['color'] as Color).withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    section['details'],
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 13,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // المميزات
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      (section['features'] as List<String>).map((feature) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color:
                            (section['color'] as Color).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: (section['color'] as Color)
                              .withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        feature,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: section['color'],
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),

                // زر الدخول
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      section['page'] != null ? 'اضغط للاستكشاف' : 'قريباً...',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: section['color'],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            (section['color'] as Color).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: section['color'],
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
