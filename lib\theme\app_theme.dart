import 'package:flutter/material.dart';

class AppTheme {
  // الألوان الرئيسية
  static const MaterialColor primarySwatch = MaterialColor(
    0xFF4F908E,
    <int, Color>{
      50: Color(0xFFE6F2F2),
      100: Color(0xFFCCE5E5),
      200: Color(0xFF99CBCA),
      300: Color(0xFF66B2B0),
      400: Color(0xFF4F908E),
      500: Color(0xFF307371),
      600: Color(0xFF266A68),
      700: Color(0xFF1D605E),
      800: Color(0xFF134544),
      900: Color(0xFF0A2B2A),
    },
  );

  // ألوان التطبيق الأساسية
  static final ThemeColors colors = ThemeColors();

  // ثيم التطبيق
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: primarySwatch,
      primaryColor: colors.primary,
      scaffoldBackgroundColor: colors.background,

      // تخصيص AppBar
      appBarTheme: AppBarTheme(
        backgroundColor: colors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),

      // تخصيص البطاقات
      // cardTheme: CardTheme(
      //   color: colors.surface,
      //   elevation: 2,
      //   shape: RoundedRectangleBorder(
      //     borderRadius: BorderRadius.circular(15),
      //   ),
      // ),

      // تخصيص الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      // تخصيص Chips
      chipTheme: ChipThemeData(
        backgroundColor: colors.primary.withOpacity(0.1),
        selectedColor: colors.primary.withOpacity(0.2),
        labelStyle: TextStyle(color: colors.primary),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: colors.primary.withOpacity(0.3)),
        ),
      ),
    );
  }

  // زخارف مشتركة
  static final decorations = AppDecorations();
}

// كلاس للألوان
class ThemeColors {
  final Color primary = const Color(0xFF4F908E);
  final Color primaryDark = const Color(0xFF307371);
  final Color secondary = const Color(0xFFD2AF53);
  final Color surface = Colors.white;
  final Color background = const Color(0xFFF5F5F5);
  final Color text = const Color(0xFF2D3142);
  final Color textLight = Colors.white;
  final Color textGrey = const Color(0xFF9A9A9A);

  // تدرجات لونية
  LinearGradient get primaryGradient => LinearGradient(
        colors: [primary, primaryDark],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      );

  LinearGradient get surfaceGradient => LinearGradient(
        colors: [surface, surface.withOpacity(0.9)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
}

// كلاس للزخارف المشتركة
class AppDecorations {
  // ظلال
  List<BoxShadow> get cardShadow => [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ];

  // زخرفة البطاقات
  BoxDecoration cardDecoration(BuildContext context) => BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(15),
        boxShadow: cardShadow,
      );

  // زخرفة القوائم
  BoxDecoration listItemDecoration(BuildContext context) => BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
        ),
      );
}
