import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/home/<USER>/_buildChatButton.dart';
import 'package:wiffada/screen/home/<USER>/_buildVirtualToursSection.dart';
import 'package:wiffada/screen/home/<USER>/_buildSeerahSection.dart';
import 'package:wiffada/screen/home/<USER>/prayer_card_widget.dart';
import 'package:wiffada/screen/home/<USER>/prayer_times_bottom_sheet.dart';
import 'package:wiffada/screen/home/<USER>/stats_section.dart';
import 'package:wiffada/screen/home/<USER>/islamic_quote_card.dart';
import 'package:wiffada/screen/mosque_services/mosque_services_page.dart';
import 'package:wiffada/theme/app_theme.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:wiffada/widgets/custom_snackbar.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/providers/prayer_provider.dart';
import 'dart:math' as math;

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _backgroundAnimationController;
  late AnimationController _floatingAnimationController;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('ar'); // إضافة تهيئة التنسيق العربي

    // تهيئة مزود مواقيت الصلاة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      Provider.of<PrayerProvider>(context, listen: false).initialize();
    });

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _backgroundAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _floatingAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _backgroundAnimationController.dispose();
    _floatingAnimationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // تم نقل هذه الدوال إلى PrayerService و PrayerProvider

  @override
  Widget build(BuildContext context) {
    final colors = AppTheme.colors;
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        toolbarHeight: 80,
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF307371),
                Color(0xFF4F908E),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
          ),
        ),
        title: Row(
          children: [
            Image.asset(
              'assets/images/wifada_logo.png',
              height: 40,
              width: 40,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  translations.translate('welcome_message'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
                Text(
                  translations.translate('app_subtitle'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 13,
                    color: Colors.white.withOpacity(0.9),
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          // زر الاشتراك
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.volunteer_activism_outlined,
                color: Colors.white,
                size: 22,
              ),
            ),
            onPressed: () {
              CustomSnackbar.show(
                context: context,
                message: AppLocalizations(
                        Provider.of<LanguageProvider>(context, listen: false)
                            .currentLanguage)
                    .translate('subscription_coming_soon'),
              );
            },
          ),
          // زر الإشعارات
          Container(
            margin: const EdgeInsets.only(left: 12),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 5,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 22,
                ),
              ),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      AppLocalizations(Provider.of<LanguageProvider>(context,
                                  listen: false)
                              .currentLanguage)
                          .translate('notifications_coming_soon'),
                      style: GoogleFonts.ibmPlexSansArabic(),
                      textAlign: TextAlign.center,
                    ),
                    duration: const Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: const Color(0xFF4F908E),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // خلفية متحركة مع تأثيرات بصرية
          AnimatedBuilder(
            animation: _backgroundAnimationController,
            builder: (context, child) {
              return Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF1A5F5D),
                      Color(0xFF2D7A77),
                      Color(0xFF4F908E),
                      Color(0xFF6BA8A6),
                    ],
                    stops: [0.0, 0.3, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    // دوائر متحركة في الخلفية
                    ...List.generate(6, (index) {
                      final double size = 100 + (index * 50);
                      final double animationOffset = (index * 0.3);
                      return Positioned(
                        left: -size / 2 +
                            (MediaQuery.of(context).size.width *
                                (0.2 + index * 0.15)) +
                            (50 *
                                math.sin((_backgroundAnimationController.value *
                                        2 *
                                        math.pi) +
                                    animationOffset)),
                        top: -size / 2 +
                            (MediaQuery.of(context).size.height *
                                (0.1 + index * 0.15)) +
                            (30 *
                                math.cos((_backgroundAnimationController.value *
                                        2 *
                                        math.pi) +
                                    animationOffset)),
                        child: Container(
                          width: size,
                          height: size,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.05),
                                Colors.white.withValues(alpha: 0.02),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
                    // تدرج علوي للانتقال السلس
                    Container(
                      height: MediaQuery.of(context).size.height * 0.4,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.white.withValues(alpha: 0.95),
                          ],
                          stops: const [0.0, 1.0],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          SafeArea(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPrayerCard(),
                  const SizedBox(height: 20),
                  // إضافة الاقتباس الإسلامي
                  const IslamicQuoteCard(),
                  const SizedBox(height: 20),
                  // إضافة قسم الإحصائيات
                  const StatsSection(),
                  const SizedBox(height: 20),
                  _buildMosqueAndMapSection(),
                  const SizedBox(height: 25),
                  const BuildChatButton(),
                  const SizedBox(height: 25),
                  const BuildSeerahSection(),
                  const SizedBox(height: 25),
                  const BuildVirtualToursSection(),
                  // const SizedBox(height: 25),
                  // const BuildEventsCard(),
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerCard() {
    return PrayerCardWidget(
      onTap: () => _showPrayerTimesDialog(context),
    );
  }

  void _showPrayerTimesDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => const PrayerTimesBottomSheet(),
    );
  }

  // تم نقل هذه الدوال إلى PrayerService و PrayerProvider

  // دالة للانتقال إلى صفحة المسجد النبوي مع انيميشن بسيط
  void _navigateToMosqueWithAnimation(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const MosqueServicesPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // انيميشن تلاشي بسيط
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  Widget _buildMosqueCard() {
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return AnimatedBuilder(
      animation: _floatingAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
              0, 5 * math.sin(_floatingAnimationController.value * math.pi)),
          child: GestureDetector(
            onTap: () => _navigateToMosqueWithAnimation(context),
            child: Container(
              height: 220,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                image: const DecorationImage(
                  image: AssetImage('assets/images/backgrounds/3.jpg'),
                  fit: BoxFit.cover,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF2D7A77).withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 10),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // تدرج لوني محسن
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          const Color(0xFF1A5F5D).withValues(alpha: 0.8),
                          const Color(0xFF0D2F2D).withValues(alpha: 0.95),
                        ],
                        stops: const [0.0, 0.6, 1.0],
                      ),
                    ),
                  ),
                  // أيقونة زخرفية
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.mosque_outlined,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  // المحتوى الرئيسي
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            translations.translate('prophets_mosque'),
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              translations.translate('explore_mosque_subtitle'),
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMapCard() {
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return AnimatedBuilder(
      animation: _floatingAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
              0, -3 * math.sin(_floatingAnimationController.value * math.pi)),
          child: GestureDetector(
            onTap: () => Navigator.pushNamed(context, '/map'),
            child: Container(
              height: 220,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                image: const DecorationImage(
                  image: AssetImage('assets/images/backgrounds/map.jpg'),
                  fit: BoxFit.cover,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFD2AF53).withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 10),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // تدرج لوني محسن
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          const Color(0xFF8B5A3C).withValues(alpha: 0.8),
                          const Color(0xFF5D3A26).withValues(alpha: 0.95),
                        ],
                        stops: const [0.0, 0.6, 1.0],
                      ),
                    ),
                  ),
                  // المحتوى الرئيسي
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // أيقونة الخريطة مع تصميم جميل
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.white.withValues(alpha: 0.9),
                                  Colors.white.withValues(alpha: 0.7),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.map_outlined,
                              color: Color(0xFF8B5A3C),
                              size: 24,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            translations.translate('map'),
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              translations.translate('explore_map'),
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMosqueAndMapSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        height: 220,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 3,
              child: _buildMosqueCard(),
            ),
            const SizedBox(width: 15),
            Expanded(
              flex: 2,
              child: _buildMapCard(),
            ),
          ],
        ),
      ),
    );
  }
}
