import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';

class Place {
  final String id;
  final String name;
  final String category;
  final String description;
  final String image;
  final LatLng location;
  final double rating;
  final String openingHours;
  final List<String> facilities;
  final List<String> tips;
  final String icon;

  Place({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.image,
    required this.location,
    required this.rating,
    required this.openingHours,
    required this.facilities,
    required this.tips,
    required this.icon,
  });

  // تحويل البيانات من JSON
  factory Place.fromJson(Map<String, dynamic> json) {
    return Place(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      description: json['description'] as String,
      image: json['image'] as String,
      location: LatLng(
        json['location']['latitude'] as double,
        json['location']['longitude'] as double,
      ),
      rating: json['rating'] as double,
      openingHours: json['openingHours'] as String,
      facilities: List<String>.from(json['facilities'] as List),
      tips: List<String>.from(json['tips'] as List),
      icon: json['icon'] as String,
    );
  }

  // تحويل البيانات إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'image': image,
      'location': {
        'latitude': location.latitude,
        'longitude': location.longitude,
      },
      'rating': rating,
      'openingHours': openingHours,
      'facilities': facilities,
      'tips': tips,
      'icon': icon,
    };
  }

  // الحصول على لون الفئة
  Color getCategoryColor() {
    switch (category) {
      case 'mosques':
        return const Color.fromARGB(255, 47, 151, 117);
      case 'mountains':
        return Colors.brown;
      case 'historical':
        return Colors.orange;
      case 'markets':
        return Colors.blue;
      case 'museums':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // الحصول على أيقونة الفئة
  IconData getCategoryIcon() {
    switch (category) {
      case 'mosques':
        return Icons.mosque;
      case 'mountains':
        return Icons.landscape;
      case 'historical':
        return Icons.history_edu;
      case 'markets':
        return Icons.store;
      case 'museums':
        return Icons.museum;
      default:
        return Icons.place;
    }
  }
}
