import 'package:flutter/material.dart';
import '../models/event.dart';
import '../services/events_service.dart';

/// مزود حالة للفعاليات والأحداث
class EventsProvider with ChangeNotifier {
  final EventsService _eventsService = EventsService();
  List<Event> _events = [];
  bool _isLoading = false;
  String _error = '';

  /// قائمة الفعاليات الحالية
  List<Event> get events => _events;
  
  /// حالة التحميل
  bool get isLoading => _isLoading;
  
  /// رسالة الخطأ إن وجدت
  String get error => _error;

  /// جلب الفعاليات من الخدمة
  Future<void> fetchEvents() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      _events = await _eventsService.getEvents();
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// جلب فعالية محددة بواسطة المعرف
  Future<Event?> getEventById(String id) async {
    try {
      return await _eventsService.getEventById(id);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// جلب الفعاليات حسب الفئة
  Future<List<Event>> getEventsByCategory(String category) async {
    try {
      return await _eventsService.getEventsByCategory(category);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// جلب الفعاليات القادمة
  Future<void> fetchUpcomingEvents() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      _events = await _eventsService.getUpcomingEvents();
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// البحث عن فعاليات
  Future<void> searchEvents(String query) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      _events = await _eventsService.searchEvents(query);
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// مسح البيانات المخزنة مؤقتاً
  void clearCache() {
    _eventsService.clearCache();
  }
}
