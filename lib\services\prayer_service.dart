import 'package:adhan/adhan.dart';
import 'package:intl/intl.dart';
import '../models/prayer_times_model.dart';

class PrayerService {
  // إحداثيات المدينة المنورة
  final coordinates = Coordinates(24.4672, 39.6112);


  PrayerTimesModel getPrayerTimes() {
    final params = CalculationMethod.umm_al_qura.getParameters();
    params.madhab = Madhab.shafi; 

    final date = DateTime.now();
    final dateComponents = DateComponents(date.year, date.month, date.day);
    final prayerTimes = PrayerTimes(coordinates, dateComponents, params);

    return PrayerTimesModel.fromPrayerTimes(prayerTimes);
  }


  // تنسيق وقت الصلاة (12 ساعة)
  String formatTime12Hour(DateTime? time) {
    if (time == null) return '--:--';
    return DateFormat.jm().format(time);
  }

  // تنسيق وقت الصلاة (24 ساعة)
  String formatTime24Hour(DateTime? time) {
    if (time == null) return '--:--';
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // الحصول على الصلاة القادمة
  Map<String, dynamic> getNextPrayer(PrayerTimesModel prayerTimes, String languageCode) {
    final now = DateTime.now();

    // قاموس بأسماء الصلوات وأوقاتها بناءً على اللغة
    final prayers = languageCode == 'en'
        ? {
            'Fajr': prayerTimes.fajr,
            'Sunrise': prayerTimes.sunrise,
            'Dhuhr': prayerTimes.dhuhr,
            'Asr': prayerTimes.asr,
            'Maghrib': prayerTimes.maghrib,
            'Isha': prayerTimes.isha,
          }
        : {
            'الفجر': prayerTimes.fajr,
            'الشروق': prayerTimes.sunrise,
            'الظهر': prayerTimes.dhuhr,
            'العصر': prayerTimes.asr,
            'المغرب': prayerTimes.maghrib,
            'العشاء': prayerTimes.isha,
          };

    // الصلاة الافتراضية (الفجر)
    final defaultPrayer = languageCode == 'en' ? 'Fajr' : 'الفجر';
    String nextPrayer = defaultPrayer;
    DateTime? nextTime = prayerTimes.fajr?.add(const Duration(days: 1));

    // البحث عن الصلاة القادمة
    prayers.forEach((name, time) {
      if (time != null &&
          time.isAfter(now) &&
          (nextTime == null || time.isBefore(nextTime!))) {
        nextPrayer = name;
        nextTime = time;
      }
    });

    return {
      'name': nextPrayer,
      'time': nextTime,
    };
  }

  // حساب الوقت المتبقي للصلاة القادمة
  Map<String, int> getRemainingTime(DateTime? prayerTime) {
    if (prayerTime == null) return {'hours': 0, 'minutes': 0};

    final now = DateTime.now();
    DateTime adjustedPrayerTime = prayerTime;

    // إذا كان وقت الصلاة قبل الوقت الحالي، يتم إضافة يوم واحد
    if (prayerTime.isBefore(now)) {
      adjustedPrayerTime = prayerTime.add(const Duration(days: 1));
    }

    final difference = adjustedPrayerTime.difference(now);
    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;

    return {
      'hours': hours,
      'minutes': minutes,
    };
  }
}
