import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({Key? key}) : super(key: key);

  @override
  State<SubscriptionPage> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  int _selectedPlan = 1;

  final List<Map<String, dynamic>> _plans = [
    {
      'title': 'شهري',
      'price': '25',
      'period': 'شهر',
      'color': const Color(0xFF4F908E),
    },
    {
      'title': '6 أشهر',
      'price': '250',
      'period': '6 أشهر',
      'color': const Color(0xFF4F908E),
      'popular': true,
    },
    {
      'title': 'سنوي',
      'price': '499',
      'period': 'سنة',
      'color': const Color(0xFF4F908E),
    },
  ];

  final Map<String, List<String>> _subscriptionBenefits = {
    'خدمات الزوار': [
      'توزيع المياه للحجاج والزوار',
      'خدمات إرشادية في المسجد النبوي',
      'مساعدة كبار السن وذوي الاحتياجات الخاصة',
      'توفير الكراسي المتحركة',
    ],
    'الخدمات الخيرية': [
      'كفالة الأيتام في المدينة المنورة',
      'دعم الأسر المحتاجة',
      'تقديم وجبات للصائمين',
      'المساهمة في مشاريع خيرية',
    ],
    'تقارير الأثر': [
      'تقرير شهري عن الخدمات المقدمة',
      'إحصائيات عن المستفيدين',
      'صور وتوثيق للأعمال الخيرية',
      'شهادة مساهمة في خدمة زوار المدينة',
    ],
  };

  final Map<String, List<String>> _monthlyReport = {
    'إحصائيات الشهر الماضي': [
      '12,500 مستفيد من خدمات الزوار',
      '8,000 عبوة ماء تم توزيعها',
      '450 كرسي متحرك تم توفيره',
      '200 يتيم تمت كفالتهم',
    ],
    'مشاريع تم إنجازها': [
      'تجهيز 5 نقاط لتوزيع المياه',
      'تطوير مركز خدمة الزوار',
      'إفطار صائم في المسجد النبوي',
      'صيانة مرافق المصلين',
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF307371),
              Color(0xFF4F908E),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                child: Row(
                  children: [
                    IconButton(
                      icon: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.arrow_back_ios_new,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 6, 91, 97)
                            .withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Image.asset(
                        'assets/images/wifada_logo.png',
                        height: 32,
                        width: 32,
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildHeader(),
                      Container(
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(30),
                          ),
                        ),
                        child: Column(
                          children: [
                            _buildBenefitsSection(), // إضافة قسم المزايا
                            _buildSubscriptionPlans(),
                            _buildMonthlyReport(),
                            _buildSubscribeButton(),
                            const SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Icon(
            Icons.volunteer_activism,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            'ساهم في خدمة زوار المدينة المنورة',
            textAlign: TextAlign.center,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اشترك الآن وكن جزءاً من منظومة خدمة ضيوف الرحمن',
            textAlign: TextAlign.center,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildMainContent() {
  //   return Container(
  //     decoration: const BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.vertical(
  //         top: Radius.circular(30),
  //       ),
  //     ),
  //     child: Column(
  //       children: [
  //         _buildBenefitsSection(),
  //         _buildSubscriptionPlans(),
  //         _buildMonthlyReport(),
  //         _buildSubscribeButton(),
  //         const SizedBox(height: 30),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildBenefitsSection() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: const Color(0xFF307371).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.volunteer_activism,
                  color: Color(0xFF307371),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'مزايا الاشتراك',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF307371),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          ..._subscriptionBenefits.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getBenefitSectionIcon(entry.key),
                        color: const Color(0xFF307371),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        entry.key,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF307371),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ...entry.value.map((benefit) => _buildBenefitItem(benefit)),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  IconData _getBenefitSectionIcon(String section) {
    switch (section) {
      case 'خدمات الزوار':
        return Icons.people_outline;
      case 'الخدمات الخيرية':
        return Icons.favorite_outline;
      case 'تقارير الأثر':
        return Icons.analytics_outlined;
      default:
        return Icons.star_outline;
    }
  }

  Widget _buildBenefitItem(String benefit) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF307371).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.check_circle_outline,
              color: Color(0xFF307371),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              benefit,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 16,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionPlans() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            'اختر خطة الاشتراك',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF307371),
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _plans.length,
            itemBuilder: (context, index) {
              final plan = _plans[index];
              final isSelected = _selectedPlan == index;

              return GestureDetector(
                onTap: () => setState(() => _selectedPlan = index),
                child: Container(
                  height: 100,
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? const LinearGradient(
                            colors: [Color(0xFF307371), Color(0xFF4F908E)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    color: isSelected ? null : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color:
                          isSelected ? Colors.transparent : Colors.grey[200]!,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF307371).withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      if (plan['popular'] == true)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.white
                                  : const Color(0xFF307371),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'مميز',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isSelected
                                    ? const Color(0xFF307371)
                                    : Colors.white,
                              ),
                            ),
                          ),
                        ),
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  plan['title'],
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: isSelected
                                        ? Colors.white
                                        : const Color(0xFF2D3142),
                                  ),
                                ),
                                Text(
                                  'لكل ${plan['period']}',
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 14,
                                    color: isSelected
                                        ? Colors.white70
                                        : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            Text(
                              '${plan['price']} ر.س',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: isSelected
                                    ? Colors.white
                                    : const Color(0xFF2D3142),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
                  .animate()
                  .fadeIn(delay: Duration(milliseconds: index * 100))
                  .slideX(
                      begin: 0.2, delay: Duration(milliseconds: index * 100));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyReport() {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF307371).withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF307371).withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color.fromARGB(255, 252, 252, 252).withOpacity(0.08),
                  const Color.fromARGB(255, 253, 253, 253).withOpacity(0.12),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1E7268).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        color: const Color(0xFF1E7268),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'تقرير الأثر الشهري',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1E7268),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                ..._monthlyReport.entries.map((entry) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        entry.key,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 12),
                      ...entry.value
                          .map((item) => _buildClickableReportItem(item)),
                      const SizedBox(height: 16),
                    ],
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableReportItem(String item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Show a subtle ripple effect but no action
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF1E7268).withOpacity(0.08),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  _getReportIcon(item),
                  color: const Color(0xFF1E7268),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  item,
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    color: Colors.grey[800],
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    )
        .animate()
        .fade(duration: const Duration(milliseconds: 300))
        .scale(begin: const Offset(0.95, 0.95), end: const Offset(1, 1));
  }

  IconData _getReportIcon(String item) {
    if (item.contains('مستفيد')) return Icons.people_outline;
    if (item.contains('ماء')) return Icons.water_drop_outlined;
    if (item.contains('كرسي')) return Icons.accessible_outlined;
    if (item.contains('يتيم')) return Icons.child_care_outlined;
    if (item.contains('نقاط')) return Icons.place_outlined;
    if (item.contains('مركز')) return Icons.business_outlined;
    if (item.contains('إفطار')) return Icons.restaurant_outlined;
    if (item.contains('صيانة')) return Icons.build_outlined;
    return Icons.star_outline;
  }

  Widget _buildSubscribeButton() {
    return Container(
      margin: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF307371), Color(0xFF4F908E)],
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF307371).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'شكراً لمساهمتك في خدمة زوار المدينة المنورة',
                style: GoogleFonts.ibmPlexSansArabic(),
              ),
              backgroundColor: const Color(0xFF307371),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          minimumSize: const Size(double.infinity, 56),
        ),
        child: Text(
          'اشترك الآن',
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
