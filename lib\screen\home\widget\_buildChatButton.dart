import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/ai_chat/ChatWithGPTPage.dart';
import 'package:lottie/lottie.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/providers/language_provider.dart';

class BuildChatButton extends StatelessWidget {
  const BuildChatButton({super.key});

  @override
  Widget build(BuildContext context) {
    final translations = AppLocalizations(Provider.of<LanguageProvider>(context).currentLanguage);
    final isRTL = Provider.of<LanguageProvider>(context).textDirection == TextDirection.rtl;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1D5B59),
            Color(0xFF307371),
            Color(0xFF4F908E),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF307371).withOpacity(0.3),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _navigateToChatWithAnimation(context);
          },
          borderRadius: BorderRadius.circular(25),
          child: Stack(
            children: [
              // أيقونة خلفية
              Positioned(
                right: isRTL ? -20 : null,
                left: isRTL ? null : -20,
                bottom: -20,
                child: Transform.scale(
                  scale: 1.2,
                  child: Opacity(
                    opacity: 0.1,
                    child: SizedBox(
                      width: 140,
                      height: 140,
                      child: Hero(
                        tag: 'ai-animation-background',
                        child: Lottie.asset(
                          'assets/animations/AI.json',
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // المحتوى الرئيسي
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // أيقونة AI المتحركة
                    Hero(
                      tag: 'ai-animation',
                      child: SizedBox(
                        width: 90,
                        height: 90,
                        child: Lottie.asset(
                          'assets/animations/AI.json',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // النصوص
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                                Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                  const Icon(
                                    Icons.auto_awesome,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    translations.translate('ai_assistant'),
                                    style: GoogleFonts.ibmPlexSansArabic(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Hero(
                                tag: 'chat-title',
                                child: Material(
                                  color: Colors.transparent,
                                  child: Text(
                                    translations.translate('chat_with_ai'),
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Flexible(
                                child: Text(
                                  translations.translate('ai_chat_description'),
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.9),
                                    height: 1.2,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToChatWithAnimation(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const ChatWithGPTPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          var begin = Offset(0.0, 1.0);
          var end = Offset.zero;
          var curve = Curves.easeOutCubic;
          
          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );
          
          // Slide transition combined with fade transition
          return SlideTransition(
            position: animation.drive(tween),
            child: FadeTransition(
              opacity: animation,
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 700),
      ),
    );
  }
}