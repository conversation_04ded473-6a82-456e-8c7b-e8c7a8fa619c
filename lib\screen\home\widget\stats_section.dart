import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../providers/language_provider.dart';
import '../../../utils/app_localizations.dart';

class StatsSection extends StatefulWidget {
  const StatsSection({super.key});

  @override
  State<StatsSection> createState() => _StatsSectionState();
}

class _StatsSectionState extends State<StatsSection>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animations = List.generate(
      4,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.2,
            0.8 + (index * 0.05),
            curve: Curves.elasticOut,
          ),
        ),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    final stats = [
      {
        'icon': Icons.mosque_outlined,
        'value': '1',
        'label': translations.translate('prophets_mosque'),
        'color': const Color(0xFF4F908E),
      },
      {
        'icon': Icons.location_on_outlined,
        'value': '50+',
        'label': translations.translate('landmarks'),
        'color': const Color(0xFFD2AF53),
      },
      {
        'icon': Icons.tour_outlined,
        'value': '25+',
        'label': translations.translate('virtual_tours'),
        'color': const Color(0xFF8B5A3C),
      },
      {
        'icon': Icons.history_edu_outlined,
        'value': '100+',
        'label': translations.translate('historical_stories'),
        'color': const Color(0xFF6B4E71),
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            translations.translate('app_statistics'),
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3142),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: stats.asMap().entries.map((entry) {
              final index = entry.key;
              final stat = entry.value;
              
              return Expanded(
                child: AnimatedBuilder(
                  animation: _animations[index],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _animations[index].value,
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              (stat['color'] as Color).withValues(alpha: 0.1),
                              (stat['color'] as Color).withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: (stat['color'] as Color).withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: stat['color'] as Color,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                stat['icon'] as IconData,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              stat['value'] as String,
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: stat['color'] as Color,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              stat['label'] as String,
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 10,
                                color: const Color(0xFF6B7280),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
