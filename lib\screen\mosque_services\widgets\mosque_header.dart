import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';

class MosqueHeader extends StatelessWidget {
  const MosqueHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // صورة الخلفية
          Image.asset(
            'assets/images/backgrounds/3.jpg',
            fit: BoxFit.cover,
          ).animate()
            .fade(duration: const Duration(milliseconds: 500)),

          // طبقة التدرج
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),

          // زر الرجوع
          Positioned(
            top: 40,
            left: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Icon<PERSON><PERSON>on(
                icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ),

          // عنوان الصفحة
          Positioned(
            bottom: 60,
            left: 20,
            right: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المسجد النبوي الشريف',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ).animate()
                  .fadeIn(delay: const Duration(milliseconds: 300))
                  .moveX(),
                const SizedBox(height: 8),
                Text(
                  'اكتشف الخدمات والمرافق المتوفرة',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ).animate()
                  .fadeIn(delay: const Duration(milliseconds: 500))
                  .moveX(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
