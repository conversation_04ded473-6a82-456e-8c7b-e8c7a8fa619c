import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class VoiceTestService {
  // اختبار الاتصال مع OpenAI API
  static Future<bool> testOpenAIConnection() async {
    try {
      log("🧪 Testing OpenAI API connection...");
      
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        log("❌ API key not found");
        return false;
      }

      // اختبار بسيط مع Chat API
      const url = "https://api.openai.com/v1/chat/completions";
      
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $apiKey",
        },
        body: jsonEncode({
          "model": "gpt-3.5-turbo",
          "messages": [
            {"role": "user", "content": "مرحبا"}
          ],
          "max_tokens": 10
        }),
      );

      if (response.statusCode == 200) {
        log("✅ OpenAI API connection successful");
        return true;
      } else {
        log("❌ OpenAI API error: ${response.statusCode} - ${response.body}");
        return false;
      }
    } catch (e) {
      log("❌ Connection test failed: $e");
      return false;
    }
  }

  // اختبار الاتصال مع Realtime API
  static Future<bool> testRealtimeAPI() async {
    try {
      log("🧪 Testing Realtime API access...");
      
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        log("❌ API key not found");
        return false;
      }

      // محاولة الاتصال بـ WebSocket
      // هذا اختبار أساسي للتحقق من صحة المفتاح
      const url = "https://api.openai.com/v1/models";
      
      final response = await http.get(
        Uri.parse(url),
        headers: {
          "Authorization": "Bearer $apiKey",
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final models = data['data'] as List;
        
        // البحث عن نماذج Realtime
        final hasRealtimeModel = models.any((model) => 
          model['id'].toString().contains('realtime'));
        
        if (hasRealtimeModel) {
          log("✅ Realtime API access confirmed");
          return true;
        } else {
          log("⚠️ Realtime models not found in account");
          return false;
        }
      } else {
        log("❌ API access error: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      log("❌ Realtime API test failed: $e");
      return false;
    }
  }

  // طباعة معلومات التشخيص
  static void printDiagnostics() {
    log("🔍 Voice Chat Diagnostics:");
    log("API Key: ${dotenv.env['OPENAI_API_KEY']?.substring(0, 10)}...");
    log("Environment loaded: ${dotenv.isEveryDefined(['OPENAI_API_KEY'])}");
  }
}
