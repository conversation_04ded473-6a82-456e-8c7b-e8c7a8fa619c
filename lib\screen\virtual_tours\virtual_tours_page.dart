import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/virtual_tours/tour_view_page.dart';
import 'package:cached_network_image/cached_network_image.dart';

class VirtualToursPage extends StatelessWidget {
  const VirtualToursPage({Key? key}) : super(key: key);

  final List<Map<String, dynamic>> tours = const [
    {
      'title': 'المسجد النبوي الشريف',
      'description': 'جولة افتراضية داخل المسجد النبوي الشريف',
      'image': 'https://cnn-arabic-images.cnn.io/cloudinary/image/upload/w_1280,h_720,c_fill,q_auto:eco/cnnarabic/2023/11/03/images/253857.jpg',
      'url': 'https://vr.qurancomplex.gov.sa/msq/',
      'duration': '20 دقيقة',
      'places': ['الروضة الشريفة', 'المنبر النبوي', 'المحراب', 'القبة الخضراء', 'الأروقة الداخلية'],
    },
    {
      'title': 'الجامعة الإسلامية',
      'description': 'جولة افتراضية داخل الجامعة الإسلامية',
      'image': 'https://iu.edu.sa/media/University/bgImg.webp',
      'url': 'https://iu.edu.sa/iuvr/islamic-univ.html',
      'duration': '10 دقائق',
      'places': ['المسجد الجامعي', 'المكتبة المركزية', 'كلية الشريعة', 'مبنى الإدارة'],
    },
    {
      'title': 'مسجد قباء',
      'description': 'استكشف أول مسجد بني في الإسلام',
      'image': 'https://qubaa.mda.gov.sa/img/about%20quba.jpg',
      'url': 'https://chameleontour.com/en/international-medical-center/',
      'duration': '10 دقائق',
      'places': ['صحن المسجد', 'المئذنة التاريخية', 'المحراب', 'موقع حجر الأساس'],
      'isAvailable': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          'الجولات الافتراضية',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Stack(
        children: [
          // خلفية متدرجة
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF4F908E),
                  Colors.white.withOpacity(0.9),
                ],
                stops: const [0.0, 0.3],
              ),
            ),
          ),
          
          // المحتوى الرئيسي
          ListView.builder(
            padding: const EdgeInsets.all(20),
            itemCount: tours.length,
            itemBuilder: (context, index) {
              return _buildTourCard(context, tours[index]);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTourCard(BuildContext context, Map<String, dynamic> tour) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: tour['isAvailable'] == false ? null : () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TourViewPage(tourData: tour),
            ),
          ),
          borderRadius: BorderRadius.circular(25),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة الجولة مع Loading Placeholder
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: tour['image'],
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[200],
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF4F908E),
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[200],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.grey[400],
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'تعذر تحميل الصورة',
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4F908E),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.timer_outlined,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              tour['duration'],
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.6),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // تفاصيل الجولة
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tour['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2D3142),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      tour['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        for (var place in tour['places'])
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4F908E).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              place,
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: const Color(0xFF4F908E),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: tour['isAvailable'] == false ? 
                            [Colors.grey.shade400, Colors.grey.shade600] :
                            [const Color(0xFF4F908E), const Color(0xFF307371)],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: InkWell(
                        onTap: () {
                          if (tour['isAvailable'] == false) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.access_time, color: Colors.white),
                                    const SizedBox(width: 8),
                                    Text(
                                      'هذه الجولة غير متاحة حالياً، سيتم تفعيلها قريباً',
                                      style: GoogleFonts.ibmPlexSansArabic(),
                                    ),
                                  ],
                                ),
                                duration: const Duration(seconds: 2),
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.grey[700],
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            );
                            return;
                          }
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              tour['isAvailable'] == false ? Icons.lock_clock : Icons.view_in_ar,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              tour['isAvailable'] == false ? 'قريباً' : 'بدء الجولة',
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
