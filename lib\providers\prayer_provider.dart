import 'dart:async';
import 'package:flutter/material.dart';
import '../models/prayer_times_model.dart';
import '../services/prayer_service.dart';

class PrayerProvider extends ChangeNotifier {
  final PrayerService _prayerService = PrayerService();
  PrayerTimesModel? _prayerTimes;
  String _currentLanguage = 'ar'; // اللغة الافتراضية
  Timer? _timer;

  // الحصول على مواقيت الصلاة
  PrayerTimesModel? get prayerTimes => _prayerTimes;

  // تعيين اللغة الحالية
  void setLanguage(String languageCode) {
    _currentLanguage = languageCode;
    notifyListeners();
  }

  // تهيئة مواقيت الصلاة
  void initialize() {
    updatePrayerTimes();

    // تحديث مواقيت الصلاة كل دقيقة
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      updatePrayerTimes();
    });

    // سيتم تحديث اللغة من خلال الاستماع لتغييرات مزود اللغة في MyApp
  }

  // تحديث مواقيت الصلاة
  void updatePrayerTimes() {
    _prayerTimes = _prayerService.getPrayerTimes();
    notifyListeners();
  }

  // تنسيق وقت الصلاة (12 ساعة)
  String formatTime12Hour(DateTime? time) {
    return _prayerService.formatTime12Hour(time);
  }

  // تنسيق وقت الصلاة (24 ساعة)
  String formatTime24Hour(DateTime? time) {
    return _prayerService.formatTime24Hour(time);
  }

  // الحصول على الصلاة القادمة
  Map<String, dynamic> getNextPrayer() {
    if (_prayerTimes == null) {
      return {
        'name': _currentLanguage == 'en' ? 'Fajr' : 'الفجر',
        'time': null,
      };
    }

    return _prayerService.getNextPrayer(_prayerTimes!, _currentLanguage);
  }

  // الحصول على الوقت المتبقي للصلاة القادمة
  Map<String, int> getRemainingTime() {
    final nextPrayer = getNextPrayer();
    return _prayerService.getRemainingTime(nextPrayer['time']);
  }

  // تنسيق الوقت المتبقي للصلاة القادمة
  String formatRemainingTime(Map<String, String> translations) {
    final remaining = getRemainingTime();
    final hours = remaining['hours'] ?? 0;
    final minutes = remaining['minutes'] ?? 0;

    if (hours > 0) {
      return '$hours ${translations['hours'] ?? 'hours'} ${translations['and'] ?? 'and'} $minutes ${translations['minutes'] ?? 'minutes'}';
    } else {
      return '$minutes ${translations['minutes'] ?? 'minutes'}';
    }
  }

  // الحصول على أيقونة الصلاة
  IconData getPrayerIcon(String prayerName) {
    switch (prayerName) {
      case 'الفجر':
      case 'Fajr':
        return Icons.brightness_3;
      case 'الشروق':
      case 'Sunrise':
        return Icons.wb_sunny_outlined;
      case 'الظهر':
      case 'Dhuhr':
        return Icons.sunny;
      case 'العصر':
      case 'Asr':
        return Icons.sunny_snowing;
      case 'المغرب':
      case 'Maghrib':
        return Icons.nightlight_round;
      case 'العشاء':
      case 'Isha':
        return Icons.nights_stay;
      default:
        return Icons.access_time;
    }
  }

  // الحصول على قائمة الصلوات
  List<Map<String, dynamic>> getPrayersList(Map<String, String> translations) {
    return [
      {'name': translations['fajr'] ?? 'Fajr', 'icon': Icons.brightness_3},
      {'name': translations['sunrise'] ?? 'Sunrise', 'icon': Icons.wb_sunny_outlined},
      {'name': translations['dhuhr'] ?? 'Dhuhr', 'icon': Icons.sunny},
      {'name': translations['asr'] ?? 'Asr', 'icon': Icons.sunny_snowing},
      {'name': translations['maghrib'] ?? 'Maghrib', 'icon': Icons.nightlight_round},
      {'name': translations['isha'] ?? 'Isha', 'icon': Icons.nights_stay},
    ];
  }

  // الحصول على وقت صلاة معينة
  DateTime? getPrayerTime(String prayerName) {
    if (_prayerTimes == null) return null;

    switch (prayerName) {
      case 'الفجر':
      case 'Fajr':
        return _prayerTimes?.fajr;
      case 'الشروق':
      case 'Sunrise':
        return _prayerTimes?.sunrise;
      case 'الظهر':
      case 'Dhuhr':
        return _prayerTimes?.dhuhr;
      case 'العصر':
      case 'Asr':
        return _prayerTimes?.asr;
      case 'المغرب':
      case 'Maghrib':
        return _prayerTimes?.maghrib;
      case 'العشاء':
      case 'Isha':
        return _prayerTimes?.isha;
      default:
        return null;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
