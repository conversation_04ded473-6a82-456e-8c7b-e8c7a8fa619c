import '../models/event.dart';
import '../data/mock_events.dart';
import 'dart:async';

/// خدمة للتعامل مع بيانات الفعاليات والأحداث
class EventsService {
  // قائمة مؤقتة للتخزين المؤقت
  List<Event>? _cachedEvents;

  // وقت آخر تحديث للبيانات المخزنة مؤقتاً
  DateTime? _lastFetchTime;

  // مدة صلاحية البيانات المخزنة مؤقتاً (5 دقائق)
  final Duration _cacheDuration = const Duration(minutes: 5);

  /// الحصول على جميع الفعاليات
  Future<List<Event>> getEvents() async {
    // التحقق من وجود بيانات مخزنة مؤقتاً وصالحة
    if (_cachedEvents != null && _lastFetchTime != null) {
      final now = DateTime.now();
      final difference = now.difference(_lastFetchTime!);

      // إذا كانت البيانات المخزنة مؤقتاً صالحة، إرجاعها
      if (difference < _cacheDuration) {
        return _cachedEvents!;
      }
    }

    // محاكاة تأخير الشبكة
    await Future.delayed(const Duration(milliseconds: 800));

    try {
      // الحصول على البيانات المؤقتة
      final events = MockEvents.getMockEvents();

      // تخزين البيانات مؤقتاً
      _cachedEvents = events;
      _lastFetchTime = DateTime.now();

      return events;
    } catch (e) {
      // إعادة إرسال الخطأ للتعامل معه في الطبقة العليا
      throw EventServiceException('فشل في الحصول على الفعاليات: ${e.toString()}');
    }
  }

  /// الحصول على فعالية محددة بواسطة المعرف
  Future<Event?> getEventById(String id) async {
    try {
      final events = await getEvents();
      return events.firstWhere((event) => event.id == id);
    } catch (e) {
      // إذا لم يتم العثور على الفعالية
      return null;
    }
  }

  /// الحصول على الفعاليات حسب الفئة
  Future<List<Event>> getEventsByCategory(String category) async {
    final events = await getEvents();
    return events.where((event) => event.category == category).toList();
  }

  /// الحصول على الفعاليات حسب الحالة
  Future<List<Event>> getEventsByStatus(String status) async {
    final events = await getEvents();
    return events.where((event) => event.status == status).toList();
  }

  /// الحصول على الفعاليات القادمة
  Future<List<Event>> getUpcomingEvents() async {
    final events = await getEvents();
    return events.where((event) => event.isUpcoming).toList();
  }

  /// الحصول على الفعاليات الجارية
  Future<List<Event>> getOngoingEvents() async {
    final events = await getEvents();
    return events.where((event) => event.isOngoing).toList();
  }

  /// البحث عن فعاليات بواسطة نص البحث
  Future<List<Event>> searchEvents(String query) async {
    if (query.isEmpty) {
      return getEvents();
    }

    final events = await getEvents();
    final lowercaseQuery = query.toLowerCase();

    return events.where((event) {
      return event.title.toLowerCase().contains(lowercaseQuery) ||
             event.description.toLowerCase().contains(lowercaseQuery) ||
             event.location.toLowerCase().contains(lowercaseQuery) ||
             event.category.toLowerCase().contains(lowercaseQuery) ||
             event.organizationName.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// مسح البيانات المخزنة مؤقتاً
  void clearCache() {
    _cachedEvents = null;
    _lastFetchTime = null;
  }
}

/// استثناء خاص بخدمة الفعاليات
class EventServiceException implements Exception {
  final String message;

  EventServiceException(this.message);

  @override
  String toString() => message;
}


