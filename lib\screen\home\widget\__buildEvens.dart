import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/models/event.dart';
import 'package:wiffada/providers/events_provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/screen/events/event_details_page.dart';
import 'package:wiffada/theme/app_theme.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:provider/provider.dart';

class BuildEventsCard extends StatefulWidget {
  const BuildEventsCard({super.key});

  @override
  State<BuildEventsCard> createState() => _BuildEventsCardState();
}

class _BuildEventsCardState extends State<BuildEventsCard> {
  bool _isLoading = true;
  List<Event> _events = [];
  String _error = '';

  @override
  void initState() {
    super.initState();
    // استخدام ميكانيكية آمنة لتأخير استدعاء Provider حتى يكتمل بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadEvents();
    });
  }

  Future<void> _loadEvents() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _error = '';
      });

      // استخدام EventsProvider لجلب الأحداث
      final eventsProvider = Provider.of<EventsProvider>(context, listen: false);
      await eventsProvider.fetchEvents();

      if (!mounted) return;

      setState(() {
        _events = eventsProvider.events.take(5).toList(); // أخذ أول 5 أحداث فقط
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final translations = AppLocalizations(Provider.of<LanguageProvider>(context).currentLanguage);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4F908E).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.event,
                      color: Color(0xFF4F908E),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    translations.translate('events_activities'),
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2D3142),
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => Navigator.pushNamed(context, '/events'),
                child: Text(
                  translations.translate('view_all'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    color: const Color(0xFF4F908E),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _isLoading
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(color: Color(0xFF4F908E)),
                  ),
                )
              : _error.isNotEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            const Icon(Icons.error_outline, color: Colors.red, size: 40),
                            const SizedBox(height: 8),
                            Text(
                              'حدث خطأ في تحميل الفعاليات',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                            TextButton(
                              onPressed: _loadEvents,
                              child: Text(
                                'إعادة المحاولة',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  color: const Color(0xFF4F908E),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : _events.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text(
                              'لا توجد فعاليات متاحة حالياً',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        )
                      : SizedBox(
                          height: 280,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            physics: const BouncingScrollPhysics(),
                            itemCount: _events.length,
                            itemBuilder: (context, index) {
                              return _buildEventCard(_events[index], context);
                            },
                          ),
                        ),
        ],
      ),
    );
}

Widget _buildEventCard(Event event, BuildContext context) {
  final colors = AppTheme.colors;
  final decorations = AppTheme.decorations;

  return GestureDetector(
    onTap: () => Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EventDetailsPage(event: event),
      ),
    ),
    child: Container(
      width: 300,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: decorations.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            child: Stack(
              children: [
                Image.network(
                  event.imageUrl,
                  height: 160,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 160,
                      width: double.infinity,
                      color: Colors.grey[200],
                      child: Icon(
                        event.icon,
                        size: 50,
                        color: Colors.grey[400],
                      ),
                    );
                  },
                ),
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.3),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(event.status),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _getStatusText(event.status),
                      style: GoogleFonts.ibmPlexSansArabic(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.calendar_today,
                        size: 16, color: Color(0xFF4F908E)),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        event.date,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.location_on,
                        size: 16, color: Color(0xFF4F908E)),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        event.location,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Color _getStatusColor(String status) {
  switch (status) {
    case 'upcoming':
      return const Color(0xFF4F908E);
    case 'ongoing':
      return Colors.orange;
    case 'completed':
      return Colors.grey;
    default:
      return Colors.grey;
  }
}

String _getStatusText(String status) {
  switch (status) {
    case 'upcoming':
      return 'قريباً';
    case 'ongoing':
      return 'جارٍ الآن';
    case 'completed':
      return 'منتهي';
    default:
      return '';
  }
}}