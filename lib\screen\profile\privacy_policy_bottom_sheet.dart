import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';

class PrivacyPolicyBottomSheet extends StatelessWidget {
  const PrivacyPolicyBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 10,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Iconsax.security, color: Color(0xFF4F908E)),
                    const SizedBox(width: 12),
                    Text(
                      'سياسة الخصوصية',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(20),
              children: [
                _buildSection(
                  title: 'مقدمة',
                  content: 'نحن نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية.',
                  icon: Iconsax.document,
                ),
                _buildSection(
                  title: 'جمع المعلومات',
                  content: '''• نجمع فقط المعلومات الضرورية لتحسين تجربة المستخدم
• لا نشارك معلوماتك مع أي طرف ثالث
• نستخدم تقنيات التشفير لحماية بياناتك''',
                  icon: Iconsax.data,
                ),
                _buildSection(
                  title: 'استخدام التطبيق',
                  content: '''• التطبيق مجاني للاستخدام
• يمكنك حذف حسابك في أي وقت
• نحتفظ بحق تعديل الخدمات المقدمة''',
                  icon: Iconsax.mobile,
                ),
                _buildSection(
                  title: 'التواصل',
                  content: 'يمكنك التواصل معنا عبر البريد الإلكتروني المذكور في قسم "اتصل بنا"',
                  icon: Iconsax.message,
                ),
                SingleChildScrollView(
                  child: Text(
                    '''
سياسة الخصوصية

نلتزم في تطبيق وفادة بحماية خصوصية مستخدمينا الكرام.

• لا نقوم بجمع أي بيانات شخصية من المستخدمين
• لا نقوم بتخزين أي معلومات دائمة، وجميع البيانات تُمسح تلقائياً عند إغلاق التطبيق
• لا نشارك أي بيانات مع أطراف ثالثة
• التطبيق لا يتطلب تسجيل حساب أو معلومات شخصية
• جميع الخدمات متاحة بدون الحاجة لتسجيل الدخول

استخدام التطبيق:
• التطبيق مجاني بالكامل
• يمكنك استخدام جميع الخدمات بدون قيود
• نحتفظ بحق تعديل وتحديث الخدمات المقدمة

للاستفسارات:
يمكنكم التواصل معنا عبر القنوات الرسمية للجمعية
                    ''',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 14,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF4F908E)),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              height: 1.5,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
