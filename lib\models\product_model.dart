import 'package:flutter/material.dart';

class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isAvailable;
  final List<String> sizes;
  final List<String> colors;
  final List<String> tags;
  final String seller;
  final String origin;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    required this.rating,
    required this.reviewCount,
    required this.isAvailable,
    required this.sizes,
    required this.colors,
    required this.tags,
    required this.seller,
    required this.origin,
  });

  // تحويل البيانات من JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      imageUrl: json['imageUrl'] as String,
      category: json['category'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      isAvailable: json['isAvailable'] as bool,
      sizes: List<String>.from(json['sizes'] as List? ?? []),
      colors: List<String>.from(json['colors'] as List? ?? []),
      tags: List<String>.from(json['tags'] as List? ?? []),
      seller: json['seller'] as String,
      origin: json['origin'] as String,
    );
  }

  // تحويل البيانات إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'category': category,
      'rating': rating,
      'reviewCount': reviewCount,
      'isAvailable': isAvailable,
      'sizes': sizes,
      'colors': colors,
      'tags': tags,
      'seller': seller,
      'origin': origin,
    };
  }

  // الحصول على لون الفئة
  Color getCategoryColor() {
    switch (category) {
      case 'dates':
        return const Color(0xFF8D6E63);
      case 'perfumes':
        return const Color(0xFF7E57C2);
      case 'souvenirs':
        return const Color(0xFF5C6BC0);
      case 'clothing':
        return const Color(0xFF26A69A);
      case 'food':
        return const Color(0xFFEF5350);
      case 'accessories':
        return const Color(0xFFFFB74D);
      default:
        return const Color(0xFF4F908E);
    }
  }

  // الحصول على أيقونة الفئة
  IconData getCategoryIcon() {
    switch (category) {
      case 'dates':
        return Icons.eco;
      case 'perfumes':
        return Icons.spa;
      case 'souvenirs':
        return Icons.card_giftcard;
      case 'clothing':
        return Icons.checkroom;
      case 'food':
        return Icons.restaurant;
      case 'accessories':
        return Icons.watch;
      default:
        return Icons.shopping_bag;
    }
  }
}
