import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/ai_chat/ChatWithGPTPage.dart';
import 'package:wiffada/screen/home/<USER>';
import 'package:wiffada/screen/mosque_services/mosque_services_page.dart';
import 'package:wiffada/screen/profile/profile_page.dart';
import 'package:wiffada/screen/map/advanced_map_page.dart';
import 'package:wiffada/screen/splash/splash_screen.dart';
import 'package:wiffada/screen/subscription/subscription_page.dart';
import 'package:wiffada/screen/virtual_tours/virtual_tours_page.dart';
import 'package:wiffada/screen/explore/explore_page.dart'; // إضافة صفحة استكشف
import 'package:wiffada/screen/volunteer/volunteer_page.dart'; // إضافة صفحة التطوع
import 'package:wiffada/screen/events/events_list_page.dart'; // إضافة صفحة الفعاليات
import 'package:wiffada/screen/seerah/seerah_main_page.dart'; // إضافة صفحة السيرة النبوية
import 'package:wiffada/screen/seerah/timeline_page.dart'; // إضافة صفحة الخط الزمني
import 'package:wiffada/screen/seerah/companions_page.dart'; // إضافة صفحة الصحابة
import 'package:wiffada/screen/seerah/interactive_events_page.dart'; // إضافة صفحة الأحداث التفاعلية
import 'package:wiffada/providers/events_provider.dart'; // إضافة مزود حالة الفعاليات
import 'package:wiffada/providers/volunteer_provider.dart'; // إضافة مزود حالة التطوع
import 'package:wiffada/providers/prayer_provider.dart'; // إضافة مزود مواقيت الصلاة
import 'package:wiffada/providers/mosque_provider.dart'; // إضافة مزود بيانات المسجد النبوي
import 'package:wiffada/utils/app_localizations.dart';
import 'package:salomon_bottom_bar/salomon_bottom_bar.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/providers/navigation_provider.dart';
import 'package:wiffada/providers/places_provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:iconsax/iconsax.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// com.tec.wiffada
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // طباعة المسار الحالي
  print('Current directory: ${Directory.current.path}');

  try {
    // تحميل ملف .env
    await dotenv.load(fileName: ".env");
    print('Successfully loaded .env file');
    print('OPENAI_API_KEY: ${dotenv.env['OPENAI_API_KEY']}');
  } catch (e) {
    print('Error loading .env file: $e');
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => NavigationProvider()),
        ChangeNotifierProvider(create: (_) => PlacesProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => EventsProvider()),
        ChangeNotifierProvider(create: (_) => VolunteerProvider()),
        ChangeNotifierProvider(create: (_) => PrayerProvider()),
        ChangeNotifierProvider(create: (_) => MosqueProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        // تحديث اللغة في مزود مواقيت الصلاة
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Provider.of<PrayerProvider>(context, listen: false)
              .setLanguage(languageProvider.currentLanguage);
        });

        return MaterialApp(
          title: 'وفادة',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: const Color(0xFF4F908E),
            fontFamily: GoogleFonts.notoKufiArabic().fontFamily,
          ),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar', ''), // العربية
            Locale('en', ''), // الإنجليزية
            Locale('ur', ''), // الأردو
          ],
          locale: Locale(languageProvider.currentLanguage),
          builder: (context, child) {
            return Directionality(
              textDirection: languageProvider.textDirection,
              child: child!,
            );
          },
          routes: {
            '/': (context) => const SplashScreen(),
            '/home': (context) => const MyHomePage(),
            '/map': (context) => const AdvancedMapPage(),
            '/chat': (context) => const ChatWithGPTPage(),
            '/prophets_mosque': (context) => const MosqueServicesPage(),
            '/virtual_tours': (context) => const VirtualToursPage(),
            '/volunteer': (context) => ChangeNotifierProvider(
                  create: (_) => VolunteerProvider(),
                  child: const VolunteerPage(),
                ), // إضافة مسار صفحة التطوع
            '/subscription': (context) => const SubscriptionPage(),
            '/events': (context) => ChangeNotifierProvider(
                  create: (_) => EventsProvider(),
                  child: const EventsListPage(),
                ), // إضافة مسار صفحة السلة
            '/seerah': (context) =>
                const SeerahMainPage(), // إضافة مسار صفحة السيرة النبوية
            '/seerah/timeline': (context) =>
                const TimelinePage(), // إضافة مسار صفحة الخط الزمني
            '/seerah/companions': (context) =>
                const CompanionsPage(), // إضافة مسار صفحة الصحابة
            '/seerah/interactive': (context) =>
                const InteractiveEventsPage(), // إضافة مسار صفحة الأحداث التفاعلية
          },
          initialRoute: '/',
        );
      },
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const Home(), // الرئيسية - 0
    const ExplorePage(), // استكشف - 1
    // const SeerahMainPage(), // السيرة النبوية - 2
    const ProfilePage(), // الملف الشخصي - 3
  ];

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);

    return Scaffold(
      //pages
      body: _pages[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SalomonBottomBar(
              currentIndex: _selectedIndex,
              onTap: (index) => setState(() => _selectedIndex = index),
              margin: EdgeInsets.zero,
              itemPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              items: [
                // الرئيسية
                SalomonBottomBarItem(
                  icon: const Icon(Iconsax.home, size: 20),
                  activeIcon: const Icon(Iconsax.home,
                      color: Color(0xFF4F908E), size: 20),
                  title: Text(
                    translations.translate('nav_home'),
                    style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  selectedColor: const Color(0xFF4F908E),
                ),
                // استكشف
                SalomonBottomBarItem(
                  icon: const Icon(Iconsax.discover, size: 20),
                  activeIcon: const Icon(Iconsax.discover,
                      color: Color(0xFF4F908E), size: 20),
                  title: Text(
                    translations.translate('nav_explore'),
                    style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  selectedColor: const Color(0xFF4F908E),
                ),
                // السيرة النبوية
                // SalomonBottomBarItem(
                //   icon: const Icon(Iconsax.book_1, size: 20),
                //   activeIcon: const Icon(Iconsax.book_1,
                //       color: Color(0xFF4F908E), size: 20),
                //   title: Text(
                //     'السيرة',
                //     style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                //     overflow: TextOverflow.ellipsis,
                //   ),
                //   selectedColor: const Color(0xFF4F908E),
                // ),
                // صفحة المتطوعين
                // SalomonBottomBarItem(
                //   icon: const Icon(Iconsax.people, size: 20),
                //   activeIcon: const Icon(Iconsax.people, color: Color(0xFF4F908E), size: 20),
                //   title: Text(
                //     translations.translate('Volunteer'),
                //     style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                //     overflow: TextOverflow.ellipsis,
                //   ),
                //   selectedColor: const Color(0xFF4F908E),
                // ),
                // // صفحة المتجر
                // SalomonBottomBarItem(
                //   icon: const Icon(Iconsax.shop, size: 20),
                //   activeIcon: const Icon(Iconsax.shop, color: Color(0xFF4F908E), size: 20),
                //   title: Text(
                //     translations.translate('nav_store'),
                //     style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                //     overflow: TextOverflow.ellipsis,
                //   ),
                //   selectedColor: const Color(0xFF4F908E),
                // ),
                // الملف الشخصي
                SalomonBottomBarItem(
                  icon: const Icon(Iconsax.profile_circle, size: 20),
                  activeIcon: const Icon(Iconsax.profile_circle,
                      color: Color(0xFF4F908E), size: 20),
                  title: Text(
                    translations.translate('nav_profile'),
                    style: GoogleFonts.ibmPlexSansArabic(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  selectedColor: const Color(0xFF4F908E),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
