import 'package:flutter/material.dart';

class AppColors {
  // الألوان الرئيسية
  static const Color primary = Color(0xFF2A9D8F);
  static const Color secondary = Color(0xFF264653);
  static const Color accent = Color(0xFFE9C46A);
  
  // تدرجات الألوان الرئيسية
  static const Color primaryLight = Color(0xFF4FB3A7);
  static const Color primaryDark = Color(0xFF1E7268);
  
  // ألوان الخلفية
  static const Color background = Color(0xFFF8F9FA);
  static const Color surface = Colors.white;
  
  // ألوان النصوص
  static const Color textPrimary = Color(0xFF2D3436);
  static const Color textSecondary = Color(0xFF636E72);
  static const Color textLight = Colors.white;
  
  // ألوان إضافية
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE74C3C);
  static const Color warning = Color(0xFFF1C40F);
  static const Color info = Color(0xFF3498DB);
  
  // ألوان الظلال
  static Color shadowColor = Colors.black.withOpacity(0.1);
  
  // تدرجات لونية
  static LinearGradient primaryGradient = const LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static LinearGradient surfaceGradient = LinearGradient(
    colors: [surface, surface.withOpacity(0.9)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
