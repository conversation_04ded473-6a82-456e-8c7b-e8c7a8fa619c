import 'package:flutter/material.dart';
import '../models/mosque_data_model.dart';
import '../services/mosque_service.dart';

class MosqueProvider extends ChangeNotifier {
  final MosqueService _mosqueService = MosqueService();
  
  // بيانات المسجد
  MosqueDataModel? _mosqueData;
  bool _isLoading = false;
  String? _error;
  
  // الحصول على بيانات المسجد
  MosqueDataModel? get mosqueData => _mosqueData;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // الحصول على بيانات البث المباشر
  LiveStreamData? get liveStreamData => _mosqueData?.liveStream;
  
  // الحصول على بيانات الإرشادات
  List<GuidelineData> get guidelinesData => _mosqueData?.guidelines ?? [];
  
  // الحصول على بيانات الخريطة
  MapData? get mapData => _mosqueData?.mapData;
  
  // الحصول على بيانات المرافق
  List<FacilityData> get facilitiesData => _mosqueData?.facilities ?? [];
  
  // جلب بيانات المسجد
  Future<void> fetchMosqueData(String mosqueId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _mosqueData = await _mosqueService.getMosqueData(mosqueId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  // البحث عن إرشاد بواسطة المعرف
  GuidelineData? getGuidelineById(String guidelineId) {
    if (_mosqueData == null) return null;
    
    try {
      return _mosqueData!.guidelines.firstWhere(
        (guideline) => guideline.id == guidelineId,
      );
    } catch (e) {
      return null;
    }
  }
  
  // البحث عن مرفق بواسطة المعرف
  FacilityData? getFacilityById(String facilityId) {
    if (_mosqueData == null) return null;
    
    try {
      return _mosqueData!.facilities.firstWhere(
        (facility) => facility.id == facilityId,
      );
    } catch (e) {
      return null;
    }
  }
  
  // تحديث بيانات المسجد
  Future<void> refreshMosqueData(String mosqueId) async {
    await fetchMosqueData(mosqueId);
  }
}
