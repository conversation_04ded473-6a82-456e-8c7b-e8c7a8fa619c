import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../data/mosque_data.dart';

class ServicesTab extends StatelessWidget {
  const ServicesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        itemCount: services.length,
        itemBuilder: (context, index) {
          final service = services[index];
          return _buildServiceCard(service);
        },
      ),
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF4F908E).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            service['icon'] as IconData,
            color: const Color(0xFF4F908E),
          ),
        ),
        title: Text(
          service['title'],
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          service['description'],
          style: GoogleFonts.ibmPlexSansArabic(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التفاصيل:',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF4F908E),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  service['details'],
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 15,
                    height: 1.5,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'أماكن التوفر:',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF4F908E),
                  ),
                ),
                const SizedBox(height: 8),
                ...(service['locations'] as List<String>).map((location) => _buildLocationItem(location)),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 20,
                      color: Color(0xFF4F908E),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'أوقات التوفر: ',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                    Text(
                      service['availability'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        color: Colors.grey[800],
                      ),
                    ),
                  ],
                ),
                if (service['languages'] != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'اللغات المتوفرة:',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF4F908E),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: (service['languages'] as List<String>).map((language) => _buildLanguageChip(language)).toList(),
                  ),
                ],
                if (service['schedule'] != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'المواعيد:',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF4F908E),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...(service['schedule'] as List<String>).map((time) => _buildScheduleItem(time)),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationItem(String location) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.location_on,
            size: 18,
            color: Color(0xFF4F908E),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              location,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 15,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageChip(String language) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFF4F908E).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4F908E).withOpacity(0.3),
        ),
      ),
      child: Text(
        language,
        style: GoogleFonts.ibmPlexSansArabic(
          color: const Color(0xFF4F908E),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildScheduleItem(String time) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.schedule,
            size: 18,
            color: Color(0xFF4F908E),
          ),
          const SizedBox(width: 8),
          Text(
            time,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 15,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
}
