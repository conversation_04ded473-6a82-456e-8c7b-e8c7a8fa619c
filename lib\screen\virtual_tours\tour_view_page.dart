import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../theme/app_colors.dart';

class TourViewPage extends StatefulWidget {
  final Map<String, dynamic> tourData;

  const TourViewPage({
    super.key,
    required this.tourData,
  });

  @override
  State<TourViewPage> createState() => _TourViewPageState();
}

class _TourViewPageState extends State<TourViewPage> {

  late final WebViewController _controller;

  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    _controller = WebViewController()

      ..setJavaScriptMode(JavaScriptMode.unrestricted)

      ..setNavigationDelegate(
        NavigationDelegate(

          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                isLoading = true; // تحديث حالة التحميل إلى قيد التقدم
              });
            }
          },
     
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                isLoading = false; // تحديث حالة التحميل إلى مكتمل
              });
            }
          },
        ),
      )
  
      ..loadRequest(Uri.parse(widget.tourData['url']));
  }

  @override
  Widget build(BuildContext context) {

    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            // عرض الويب الذي يملأ الشاشة
            Positioned.fill(
              child: WebViewWidget(controller: _controller),
            ),
            
            // شاشة التحميل التي تظهر أثناء تحميل المحتوى
            if (isLoading)
              Container(
                width: double.infinity,
                height: double.infinity,
                // خلفية متدرجة للشاشة أثناء التحميل
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primary.withOpacity(0.95),
                      AppColors.primary.withOpacity(0.8),
                    ],
                  ),
                ),
                // مركز شاشة التحميل
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    // تصميم مربع التحميل
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    // محتوى مربع التحميل
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // مؤشر التحميل الدائري
                        const SizedBox(
                          width: 50,
                          height: 50,
                          child: CircularProgressIndicator(
                            color: Color(0xFF4F908E),
                          ),
                        ),
                        const SizedBox(height: 24),
                        // نص التحميل
                        Text(
                          'جاري تحميل الجولة الافتراضية',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4F908E),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            
            // زر الإغلاق للعودة إلى الصفحة السابقة
            Positioned(
              top: MediaQuery.of(context).padding.top + 8,
              left: 8,
              child: FloatingActionButton.small(
                backgroundColor: Colors.white,
                elevation: 2,
                child: const Icon(
                  Icons.close,
                  color: Colors.black,
                  size: 20,
                ),
                onPressed: () => Navigator.pop(context), // الرجوع للصفحة السابقة
              ),
            ),
          ],
        ),
      ),
    );
  }
}
