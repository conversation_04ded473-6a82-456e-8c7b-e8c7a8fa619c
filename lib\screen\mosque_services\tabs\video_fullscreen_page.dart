import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class VideoFullscreenPage extends StatefulWidget {
  final String videoId;
  final bool autoPlay;

  const VideoFullscreenPage({
    Key? key,
    required this.videoId,
    this.autoPlay = true,
  }) : super(key: key);

  @override
  State<VideoFullscreenPage> createState() => _VideoFullscreenPageState();
}

class _VideoFullscreenPageState extends State<VideoFullscreenPage> {
  late YoutubePlayerController _controller;
  bool isLiveVideo = false;

  @override
  void initState() {
    super.initState();

    // تحديد ما إذا كان الفيديو بثًا مباشرًا
    isLiveVideo = widget.videoId == 'AG3Ewuhslp4';

    // تهيئة مشغل اليوتيوب
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoId,
      flags: YoutubePlayerFlags(
        autoPlay: widget.autoPlay,
        mute: false,
        disableDragSeek: isLiveVideo, // تعطيل إمكانية السحب في البث المباشر فقط
        loop: false,
        isLive: isLiveVideo, // البث المباشر فقط
        forceHD: true,
        enableCaption: false,
        hideControls: isLiveVideo, // إخفاء عناصر التحكم للبث المباشر فقط
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // مشغل الفيديو في المنتصف
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      // تبديل حالة التشغيل عند النقر على الفيديو (للبث المباشر فقط)
                      if (isLiveVideo) {
                        if (_controller.value.isPlaying) {
                          _controller.pause();
                        } else {
                          _controller.play();
                        }
                        setState(() {});
                      }
                    },
                    child: YoutubePlayer(
                      controller: _controller,
                      showVideoProgressIndicator: !isLiveVideo, // إخفاء شريط التقدم للبث المباشر
                      progressIndicatorColor: const Color(0xFF4F908E),
                      // تعطيل شريط التقدم للبث المباشر
                      onReady: () {
                        // لا نحتاج لشريط التقدم في البث المباشر
                      },
                      onEnded: (metaData) {
                        Navigator.pop(context);
                      },
                      // تخصيص أزرار التحكم للفيديو العادي فقط
                      bottomActions: isLiveVideo
                        ? const [] // لا أزرار للبث المباشر
                        : const [
                            // للفيديو العادي: عرض شريط التقدم والوقت
                            CurrentPosition(),
                            SizedBox(width: 8),
                            ProgressBar(
                              isExpanded: true,
                              colors: ProgressBarColors(
                                playedColor: Color(0xFF4F908E),
                                handleColor: Colors.white,
                                bufferedColor: Color(0xFF307371),
                                backgroundColor: Colors.black,
                              ),
                            ),
                            SizedBox(width: 8),
                            RemainingDuration(),
                          ],
                    ),
                  ),
                  // زر تشغيل/إيقاف مخصص للبث المباشر
                  if (isLiveVideo)
                    ValueListenableBuilder<YoutubePlayerValue>(
                      valueListenable: _controller,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: !value.isPlaying,
                          child: Center(
                            child: GestureDetector(
                              onTap: () {
                                _controller.play();
                              },
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.6),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 10,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.play_arrow,
                                  color: Colors.white,
                                  size: 40,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  // إضافة علامة البث المباشر كعنصر منفصل للبث المباشر فقط
                  if (isLiveVideo)
                    Positioned(
                      bottom: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.circle, color: Colors.red, size: 12),
                            SizedBox(width: 5),
                            Text(
                              'بث مباشر',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // زر الإغلاق
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
