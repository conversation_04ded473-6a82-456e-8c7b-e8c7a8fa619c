import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';

class AboutBottomSheet extends StatelessWidget {
  const AboutBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 10,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Iconsax.info_circle, color: Color(0xFF4F908E)),
                    const SizedBox(width: 12),
                    Text(
                      'عن التطبيق',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(20),
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 24),
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/wifada_logo.png',
                        height: 100,
                        width: 100,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'تطبيق وفادة',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF4F908E),
                        ),
                      ),
                      Text(
                        'النسخة 1.0.0',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildInfoSection(
                  title: 'عن المشروع',
                    content:
                    'تم تطوير هذا التطبيق كمشروع تخرج في تخصص علوم الحاسب بجامعة طيبة، باستخدام أحدث التقنيات والأدوات البرمجية. يهدف التطبيق إلى خدمة زوار المدينة المنورة بشكل شامل من خلال منصة متكاملة تقدمها جمعية وفادة الخيرية. يوفر التطبيق واجهة تفاعلية متطورة تساعد الزائرين على الاستفادة القصوى من زيارتهم للمدينة المنورة عبر خدمات الإرشاد والدليل السياحي الرقمي والمعلومات . كما يدعم التطبيق المتطوعين بأدوات إدارية متقدمة لتنظيم جهودهم وتحقيق التكامل في العمل التطوعي. يعد هذا المشروع جزءاً من مساهمات جمعية وفادة نحو تحقيق رؤية المملكة 2030 في تعزيز تجربة الزائرين وتطوير الخدمات الرقمية.',
                  icon: Iconsax.document,
                ),
                _buildInfoSection(
                  title: 'المشرف الأكاديمي',
                  content: 'أ.د أيمن نور',
                  icon: Iconsax.teacher,
                ),
                _buildInfoSection(
                  title: 'المطور وقائد الفريق',
                  content: 'مازن عبدالمنعم الأحمدي',
                  icon: Iconsax.profile_2user,
                ),
                _buildInfoSection(
                  title: 'فريق العمل',
                  content: '''• فيصل علاوي
• أسامة الحربي
• فارس الحربي''',
                  icon: Iconsax.people,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF4F908E)),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              height: 1.5,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
