import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../providers/prayer_provider.dart';
import '../../../providers/language_provider.dart';
import '../../../theme/app_theme.dart';
import '../../../utils/app_localizations.dart';

class PrayerCardWidget extends StatelessWidget {
  final VoidCallback onTap;

  const PrayerCardWidget({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colors = AppTheme.colors;
    final decorations = AppTheme.decorations;
    final languageProvider = Provider.of<LanguageProvider>(context);
    final prayerProvider = Provider.of<PrayerProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);

    // لا نقوم بتحديث اللغة هنا لتجنب استدعاء notifyListeners أثناء البناء

    // الحصول على الصلاة القادمة
    final nextPrayer = prayerProvider.getNextPrayer();
    final nextPrayerName = nextPrayer['name'] as String;

    // الحصول على الوقت المتبقي
    final translationsMap = {
      'hours': translations.translate('hours'),
      'minutes': translations.translate('minutes'),
      'and': translations.translate('and'),
    };
    final remainingTime = prayerProvider.formatRemainingTime(translationsMap);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.fromLTRB(20, 16, 20, 0),
        decoration: BoxDecoration(
          gradient: colors.primaryGradient,
          borderRadius: BorderRadius.circular(20),
          boxShadow: decorations.cardShadow,
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              top: -20,
              child: Icon(
                Icons.mosque_outlined,
                size: 100,
                color: Colors.white.withOpacity(0.1),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Icon(
                      prayerProvider.getPrayerIcon(nextPrayerName),
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          nextPrayerName,
                          style: GoogleFonts.ibmPlexSansArabic(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          translations.translate('next_prayer'),
                          style: GoogleFonts.ibmPlexSansArabic(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.timer_outlined,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          remainingTime,
                          style: GoogleFonts.ibmPlexSansArabic(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
