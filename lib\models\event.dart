import 'package:flutter/material.dart';

/// نموذج بيانات للفعاليات والأحداث في المدينة المنورة
class Event {
  final String id;
  final String title;
  final String description;
  final String location;
  final String imageUrl;
  final String date;
  final String time;
  final String category;
  final String organizationName;
  final List<String> features;
  final String status; // 'upcoming', 'ongoing', 'completed'
  final String registrationLink;
  final List<String> photos;
  final IconData icon;

  Event({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.imageUrl,
    required this.date,
    required this.time,
    required this.category,
    required this.organizationName,
    required this.features,
    required this.status,
    required this.registrationLink,
    required this.photos,
    required this.icon,
  });

  /// إنشاء كائن Event من بيانات JSON
  factory Event.fromJson(Map<String, dynamic> json, {IconData defaultIcon = Icons.event}) {
    return Event(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      category: json['category'] ?? '',
      organizationName: json['organizationName'] ?? '',
      features: List<String>.from(json['features'] ?? []),
      status: json['status'] ?? 'upcoming',
      registrationLink: json['registrationLink'] ?? '',
      photos: List<String>.from(json['photos'] ?? []),
      icon: json['icon'] != null ?
            IconData(int.parse(json['icon']), fontFamily: 'MaterialIcons') :
            defaultIcon,
    );
  }

  /// تحويل كائن Event إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'imageUrl': imageUrl,
      'date': date,
      'time': time,
      'category': category,
      'organizationName': organizationName,
      'features': features,
      'status': status,
      'registrationLink': registrationLink,
      'photos': photos,
      'icon': icon.codePoint.toString(),
    };
  }

  /// التحقق مما إذا كان الحدث قادماً
  bool get isUpcoming => status == 'upcoming';

  /// التحقق مما إذا كان الحدث جارياً
  bool get isOngoing => status == 'ongoing';

  /// التحقق مما إذا كان الحدث مكتملاً
  bool get isCompleted => status == 'completed';

  /// إنشاء نسخة معدلة من الحدث الحالي
  Event copyWith({
    String? id,
    String? title,
    String? description,
    String? location,
    String? imageUrl,
    String? date,
    String? time,
    String? category,
    String? organizationName,
    List<String>? features,
    String? status,
    String? registrationLink,
    List<String>? photos,
    IconData? icon,
  }) {
    return Event(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      imageUrl: imageUrl ?? this.imageUrl,
      date: date ?? this.date,
      time: time ?? this.time,
      category: category ?? this.category,
      organizationName: organizationName ?? this.organizationName,
      features: features ?? this.features,
      status: status ?? this.status,
      registrationLink: registrationLink ?? this.registrationLink,
      photos: photos ?? this.photos,
      icon: icon ?? this.icon,
    );
  }
}
