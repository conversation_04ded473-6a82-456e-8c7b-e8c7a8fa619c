class ContentModel {
  final String id;
  final String title;
  final String type;
  final List<ContentSection> sections;

  ContentModel({
    required this.id,
    required this.title,
    required this.type,
    required this.sections,
  });

  factory ContentModel.fromJson(Map<String, dynamic> json) {
    return ContentModel(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      sections: (json['sections'] as List)
          .map((section) => ContentSection.fromJson(section))
          .toList(),
    );
  }

  String? get imageUrl => null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'type': type,
        'sections': sections.map((section) => section.toJson()).toList(),
      };
}

class ContentSection {
  final String title;
  final List<String> items;
  final String description;

  ContentSection({
    required this.title,
    required this.items,
    this.description = '',  
  });

  factory ContentSection.fromJson(Map<String, dynamic> json) {
    return ContentSection(
      title: json['title'] as String,
      items: List<String>.from(json['items']),
      description: json['description'] as String? ?? '',  
    );
  }

  Map<String, dynamic> toJson() => {
        'title': title,
        'items': items,
        'description': description,
      };
}
