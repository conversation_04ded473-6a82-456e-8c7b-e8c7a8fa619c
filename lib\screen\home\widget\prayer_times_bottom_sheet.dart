import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../providers/prayer_provider.dart';
import '../../../providers/language_provider.dart';
import '../../../utils/app_localizations.dart';

class PrayerTimesBottomSheet extends StatelessWidget {
  const PrayerTimesBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final prayerProvider = Provider.of<PrayerProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);

    // لا نقوم بتحديث اللغة هنا لتجنب استدعاء notifyListeners أثناء البناء

    // الحصول على التاريخ الحالي
    final now = DateTime.now();
    final dateFormat = DateFormat.yMMMMd(languageProvider.currentLanguage);
    final formattedDate = dateFormat.format(now);

    // الحصول على الصلاة القادمة
    final nextPrayer = prayerProvider.getNextPrayer();
    final nextPrayerName = nextPrayer['name'] as String;

    // الحصول على الوقت المتبقي
    final translationsMap = {
      'hours': translations.translate('hours'),
      'minutes': translations.translate('minutes'),
      'and': translations.translate('and'),
    };
    final remainingTime = prayerProvider.formatRemainingTime(translationsMap);

    // الحصول على قائمة الصلوات
    final prayersList = prayerProvider.getPrayersList({
      'fajr': translations.translate('fajr'),
      'sunrise': translations.translate('sunrise'),
      'dhuhr': translations.translate('dhuhr'),
      'asr': translations.translate('asr'),
      'maghrib': translations.translate('maghrib'),
      'isha': translations.translate('isha'),
    });

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 50,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(3),
            ),
          ),

          // العنوان
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 30, 20, 20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4F908E).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.mosque_outlined,
                    color: Color(0xFF4F908E),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        translations.translate('prayer_times'),
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2D3142),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4F908E).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: Color(0xFF4F908E),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              translations.translate('madinah'),
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: const Color(0xFF4F908E),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // زر التحديث
                IconButton(
                  onPressed: () {
                    prayerProvider.updatePrayerTimes();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          translations.translate('prayer_times_updated'),
                          style: GoogleFonts.ibmPlexSansArabic(),
                        ),
                        backgroundColor: const Color(0xFF4F908E),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: const Icon(
                    Icons.refresh,
                    color: Color(0xFF4F908E),
                    size: 24,
                  ),
                  tooltip: translations.translate('refresh'),
                ),
              ],
            ),
          ),

          // معلومات إضافية
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.calendar_today,
                      color: Color(0xFF4F908E),
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      formattedDate,
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2D3142),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.timer_outlined,
                      color: Color(0xFF4F908E),
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '$nextPrayerName: $remainingTime',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 10),

          // شبكة مواقيت الصلاة
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  childAspectRatio: 1.1,
                ),
                itemCount: prayersList.length,
                itemBuilder: (context, index) {
                  final prayer = prayersList[index];
                  final prayerName = prayer['name'] as String;
                  final prayerIcon = prayer['icon'] as IconData;
                  final prayerTime = prayerProvider.getPrayerTime(prayerName);
                  final formattedTime = prayerProvider.formatTime12Hour(prayerTime);
                  final isNext = nextPrayerName == prayerName;

                  return _buildPrayerTimeBox(
                    context,
                    prayerName,
                    prayerIcon,
                    formattedTime,
                    isNext,
                  );
                },
              ),
            ),
          ),

          // مساحة إضافية في الأسفل
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildPrayerTimeBox(
    BuildContext context,
    String name,
    IconData icon,
    String time,
    bool isNext,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: isNext
            ? const LinearGradient(
                colors: [Color(0xFF4F908E), Color(0xFF307371)],
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
              )
            : null,
        color: isNext ? null : const Color(0xFF4F908E).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isNext ? Colors.white.withOpacity(0.2) : Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: isNext ? Colors.white : const Color(0xFF4F908E),
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            name,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isNext ? Colors.white : const Color(0xFF2D3142),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            time,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isNext ? Colors.white : const Color(0xFF4F908E),
            ),
          ),
        ],
      ),
    );
  }
}
