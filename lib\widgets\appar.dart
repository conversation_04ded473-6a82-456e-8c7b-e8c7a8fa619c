import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final bool showSearchBar;
  final VoidCallback? onSearchTap;
  final Function(String)? onLanguageChanged;
  final Function(String)? onSearch;
  final String currentLanguage;

  const CustomAppBar({
    super.key,
    this.title = "وفادة",
    this.showSearchBar = true,
    this.onSearchTap,
    this.onLanguageChanged,
    this.onSearch,
    this.currentLanguage = 'العربية',
  });

  @override
  Size get preferredSize => const Size.fromHeight(65);

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();
}

class _CustomAppBarState extends State<CustomAppBar> {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        toolbarHeight: 65,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(25),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF4F908E),
                const Color(0xFF2D5453),
              ],
            ),
          ),
        ),
        leading: _isSearching
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                iconSize: isSmallScreen ? 20 : 24,
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                  });
                },
              )
            : Padding(
                padding: EdgeInsets.all(isSmallScreen ? 10.0 : 12.0),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      'assets/images/wifada_logo.png',
                      width: isSmallScreen ? 28 : 32,
                      height: isSmallScreen ? 28 : 32,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
        titleSpacing: 0,
        centerTitle: true,
        title: _isSearching
            ? Container(
                height: 40,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _searchController,
                  autofocus: true,
                  style: GoogleFonts.ibmPlexSansArabic(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 13 : 15,
                  ),
                  decoration: InputDecoration(
                    hintText: 'ابحث هنا...',
                    hintStyle: GoogleFonts.ibmPlexSansArabic(
                      color: Colors.white70,
                      fontSize: isSmallScreen ? 13 : 15,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Colors.white70,
                      size: 20,
                    ),
                  ),
                  onChanged: widget.onSearch,
                  textAlign: TextAlign.right,
                ),
              )
            : Text(
                widget.title,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: isSmallScreen ? 18 : 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
        actions: [
          // Search Icon
          if (widget.showSearchBar)
            Container(
              margin: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 10 : 12,
                horizontal: isSmallScreen ? 3 : 4,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white.withOpacity(0.1),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: SizedBox(
                width: isSmallScreen ? 28 : 32,
                height: isSmallScreen ? 28 : 32,
                child: IconButton(
                  padding: EdgeInsets.zero,
                  icon: Icon(
                    _isSearching ? Icons.close : Icons.search,
                    color: Colors.white,
                    size: isSmallScreen ? 16 : 18,
                  ),
                  onPressed: () {
                    setState(() {
                      _isSearching = !_isSearching;
                      if (!_isSearching) {
                        _searchController.clear();
                      }
                    });
                  },
                ),
              ),
            ),
          // Language Switcher
          if (!_isSearching)
            Container(
              margin: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 10 : 12,
                horizontal: isSmallScreen ? 3 : 4,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white.withOpacity(0.1),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: SizedBox(
                width: isSmallScreen ? 28 : 32,
                height: isSmallScreen ? 28 : 32,
                child: PopupMenuButton<String>(
                  padding: EdgeInsets.zero,
                  icon: Icon(
                    Icons.language,
                    color: Colors.white,
                    size: isSmallScreen ? 16 : 18,
                  ),
                  offset: const Offset(0, 40),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  onSelected: widget.onLanguageChanged,
                  itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                    PopupMenuItem<String>(
                      value: 'العربية',
                      height: 40,
                      child: Row(
                        children: [
                          Icon(
                            Icons.check,
                            color: const Color(0xFF4F908E),
                            size: isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            'العربية',
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'English',
                      height: 40,
                      child: Row(
                        children: [
                          SizedBox(width: isSmallScreen ? 20 : 24),
                          Text(
                            'English',
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          SizedBox(width: isSmallScreen ? 8 : 12),
        ],
      ),
    );
  }
}
