import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

// استيراد المزودات
import '../../providers/mosque_provider.dart';

// استيراد الويدجت المخصصة
import 'widgets/mosque_tab_bar.dart';

// استيراد التبويبات
import 'tabs/live_stream_tab.dart';
import 'tabs/map_tab.dart';
import 'tabs/guidelines_tab.dart';
import 'tabs/services_tab.dart';
import 'tabs/virtual_tour_tab.dart';

class MosqueServicesPage extends StatefulWidget {
  const MosqueServicesPage({super.key});

  @override
  State<MosqueServicesPage> createState() => _MosqueServicesPageState();
}

class _MosqueServicesPageState extends State<MosqueServicesPage> {
  int _selectedTab = 0;
  bool _isFullScreen = false;

  @override
  void initState() {
    super.initState();
    // تعيين اتجاه الشاشة للوضع العمودي فقط
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تهيئة بيانات المسجد النبوي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      final mosqueProvider = Provider.of<MosqueProvider>(context, listen: false);
      mosqueProvider.fetchMosqueData('prophets_mosque');
    });
  }

  @override
  void dispose() {
    // إعادة تعيين اتجاه الشاشة إلى الوضع العمودي فقط
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullScreen) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.black,
          leading: IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => setState(() => _isFullScreen = false),
          ),
        ),
        backgroundColor: Colors.black,
        body: const Center(
          child: Text('وضع ملء الشاشة', style: TextStyle(color: Colors.white)),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 220, // زيادة ارتفاع AppBar أكثر
        automaticallyImplyLeading: false,
        flexibleSpace: Stack(
          children: [
            // صورة الخلفية
            Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/backgrounds/3.jpg'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            // طبقة سوداء شفافة مع تدرج لوني
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.6),
                    const Color(0xFF307371).withOpacity(0.7),
                    Colors.black.withOpacity(0.6),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
            // محتوى الـ AppBar
            Positioned(
              bottom: 40, // زيادة المسافة من الأسفل
              right: 20, // تغيير من left إلى right
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Text(
                  'المسجد النبوي الشريف',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            // زر الرجوع (يمين للعربية، يسار للإنجليزية)
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              right: 15, // تغيير من left إلى right للغة العربية
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white), // أيقونة العودة الصحيحة
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
          ],
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          // مساحة للـ AppBar
          SizedBox(height: MediaQuery.of(context).padding.top + 220),

          // شريط التبويب المخصص
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: MosqueTabBar(
              selectedTab: _selectedTab,
              onTabSelected: (index) {
                setState(() {
                  _selectedTab = index;
                });
              },
            ),
          ),

          // محتوى التبويب
          _buildTabContent(),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    final tabs = [
      const LiveStreamTab(),
      const MapTab(),
      const GuidelinesTab(),
      const ServicesTab(),
      const VirtualTourTab(),
    ];
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        margin: const EdgeInsets.symmetric(horizontal: 0),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(30),
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              // توحيد الانيميشن لجميع التبويبات (من أعلى إلى أسفل)
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, -0.2), // من أعلى
                  end: Offset.zero,
                ).animate(animation),
                child: FadeTransition(
                  opacity: animation,
                  child: child,
                ),
              );
            },
            child: tabs[_selectedTab],
          ),
        ),
      ),
    );
  }
}
