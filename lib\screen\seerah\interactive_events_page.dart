import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';

class InteractiveEventsPage extends StatefulWidget {
  const InteractiveEventsPage({super.key});

  @override
  State<InteractiveEventsPage> createState() => _InteractiveEventsPageState();
}

class _InteractiveEventsPageState extends State<InteractiveEventsPage> {
  int selectedEventIndex = 0;

  final List<Map<String, dynamic>> interactiveEvents = [
    {
      'title': 'غار حراء - نزول الوحي',
      'description': 'عش لحظة نزول الوحي الأولى في غار حراء',
      'type': 'واقع افتراضي',
      'duration': '15 دقيقة',
      'difficulty': 'مبتدئ',
      'participants': '1,234',
      'rating': 4.9,
      'image': null,
      'features': [
        'جولة ثلاثية الأبعاد في الغار',
        'محاكاة صوتية للوحي',
        'تفاعل مع البيئة المحيطة',
        'شرح تفصيلي للحدث'
      ],
      'icon': Iconsax.book_1,
      'color': const Color(0xFF9B59B6),
      'isAvailable': true,
    },
    {
      'title': 'رحلة الهجرة',
      'description': 'اتبع خطوات النبي ﷺ وأبي بكر في رحلة الهجرة',
      'type': 'محاكاة تفاعلية',
      'duration': '25 دقيقة',
      'difficulty': 'متوسط',
      'participants': '987',
      'rating': 4.8,
      'image': null,
      'features': [
        'خريطة تفاعلية للرحلة',
        'اتخاذ قرارات الطريق',
        'مواجهة التحديات',
        'تعلم الدروس والعبر'
      ],
      'icon': Iconsax.location,
      'color': const Color(0xFF27AE60),
      'isAvailable': true,
    },
    {
      'title': 'بناء المسجد النبوي',
      'description': 'شارك في بناء أول مسجد في الإسلام',
      'type': 'لعبة بناء',
      'duration': '20 دقيقة',
      'difficulty': 'سهل',
      'participants': '2,156',
      'rating': 4.7,
      'image': null,
      'features': [
        'بناء تفاعلي للمسجد',
        'تعلم أجزاء المسجد',
        'العمل مع الصحابة',
        'فهم أهمية المسجد'
      ],
      'icon': Iconsax.building,
      'color': const Color(0xFF3498DB),
      'isAvailable': true,
    },
    {
      'title': 'فتح مكة',
      'description': 'اشهد الفتح المبين وتطهير الكعبة',
      'type': 'مشهد سينمائي',
      'duration': '30 دقيقة',
      'difficulty': 'متقدم',
      'participants': '756',
      'rating': 4.9,
      'image': null,
      'features': [
        'مشاهد ثلاثية الأبعاد',
        'تفاعل مع الأحداث',
        'فهم استراتيجية الفتح',
        'مشاهدة تطهير الكعبة'
      ],
      'icon': Iconsax.flag,
      'color': const Color(0xFFE74C3C),
      'isAvailable': false,
    },
    {
      'title': 'حجة الوداع',
      'description': 'احضر خطبة الوداع وتعلم مناسك الحج',
      'type': 'تجربة تعليمية',
      'duration': '35 دقيقة',
      'difficulty': 'متوسط',
      'participants': '1,543',
      'rating': 4.8,
      'image': null,
      'features': [
        'حضور الخطبة التاريخية',
        'تعلم مناسك الحج',
        'التفاعل مع الحجاج',
        'فهم رسالة الوداع'
      ],
      'icon': Iconsax.home_2,
      'color': const Color(0xFFF39C12),
      'isAvailable': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          'الأحداث التفاعلية',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Row(
              children: [
                _buildEventsList(),
                _buildEventDetails(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF4F908E),
            const Color(0xFF4F908E).withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Iconsax.video_play,
            size: 48,
            color: Colors.white,
          ).animate().scale(duration: const Duration(milliseconds: 600)),
          const SizedBox(height: 12),
          Text(
            'عش الأحداث التاريخية',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
          const SizedBox(height: 8),
          Text(
            'تجارب تفاعلية غامرة لأهم أحداث السيرة النبوية',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
        ],
      ),
    );
  }

  Widget _buildEventsList() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFE0E0E0), width: 1),
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: interactiveEvents.length,
        itemBuilder: (context, index) {
          return _buildEventCard(index);
        },
      ),
    );
  }

  Widget _buildEventCard(int index) {
    final event = interactiveEvents[index];
    final isSelected = index == selectedEventIndex;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedEventIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF4F908E).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? const Color(0xFF4F908E) : Colors.grey[200]!,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event image
            Container(
              height: 120,
              decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(14)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    (event['color'] as Color).withValues(alpha: 0.1),
                    (event['color'] as Color).withValues(alpha: 0.3),
                  ],
                ),
                border: Border.all(
                  color: (event['color'] as Color).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      event['icon'],
                      size: 40,
                      color: event['color'],
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            event['isAvailable'] ? Colors.green : Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        event['isAvailable'] ? 'متاح' : 'قريباً',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Event info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event['title'],
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C3E50),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event['type'],
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 12,
                      color: event['color'],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Iconsax.clock,
                        size: 12,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        event['duration'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Iconsax.star1,
                        size: 12,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${event['rating']}',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 11,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideX(begin: -0.3, end: 0);
  }

  Widget _buildEventDetails() {
    final event = interactiveEvents[selectedEventIndex];

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Event header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: (event['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      event['icon'],
                      size: 32,
                      color: event['color'],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event['title'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C3E50),
                          ),
                        ),
                        Text(
                          event['type'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            color: event['color'],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Event image
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      (event['color'] as Color).withValues(alpha: 0.1),
                      (event['color'] as Color).withValues(alpha: 0.3),
                    ],
                  ),
                  border: Border.all(
                    color: (event['color'] as Color).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        event['icon'],
                        size: 80,
                        color: event['color'],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        event['title'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: event['color'],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Event stats
              Row(
                children: [
                  _buildStatChip(Iconsax.clock, event['duration'], 'المدة'),
                  const SizedBox(width: 12),
                  _buildStatChip(Iconsax.level, event['difficulty'], 'المستوى'),
                  const SizedBox(width: 12),
                  _buildStatChip(
                      Iconsax.people, '${event['participants']}', 'مشارك'),
                ],
              ),
              const SizedBox(height: 24),

              // Description
              Text(
                'وصف التجربة',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                event['description'],
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  color: Colors.grey[700],
                  height: 1.6,
                ),
              ),
              const SizedBox(height: 24),

              // Features
              Text(
                'مميزات التجربة',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 12),
              ...((event['features'] as List<String>).map((feature) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Iconsax.tick_circle,
                        size: 16,
                        color: event['color'],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          feature,
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList()),
              const SizedBox(height: 24),

              // Action button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: event['isAvailable']
                      ? () {
                          _startInteractiveExperience(event);
                        }
                      : null,
                  icon: Icon(
                    event['isAvailable'] ? Iconsax.play_circle : Iconsax.clock,
                    color: Colors.white,
                  ),
                  label: Text(
                    event['isAvailable'] ? 'ابدأ التجربة' : 'قريباً',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        event['isAvailable'] ? event['color'] : Colors.grey,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              Text(
                label,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _startInteractiveExperience(Map<String, dynamic> event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(event['icon'], color: event['color']),
            const SizedBox(width: 8),
            Text(
              'بدء التجربة',
              style: GoogleFonts.ibmPlexSansArabic(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: (event['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Iconsax.video_play,
                size: 64,
                color: event['color'],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'ستبدأ تجربة "${event['title']}" التفاعلية الآن. تأكد من وجود اتصال جيد بالإنترنت.',
              style: GoogleFonts.ibmPlexSansArabic(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // هنا سيتم تشغيل التجربة التفاعلية
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'جاري تحميل التجربة التفاعلية...',
                    style: GoogleFonts.ibmPlexSansArabic(),
                  ),
                  backgroundColor: event['color'],
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: event['color'],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'ابدأ',
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
